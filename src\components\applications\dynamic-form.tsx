"use client";

import React, {
  useEffect,
  use<PERSON>allback,
  useMemo,
  useRef,
  useState,
} from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Save, RefreshCw } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import {
  validateForm,
  getFieldConfigs,
  formatValidationErrors,
  ValidationResult,
} from "@/utils/form-validation";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface DynamicFormProps {
  fields: IWorkflowFormField[] | IApplicationFormField[];
  formData: Record<string, any>;
  onFormDataChange: (data: Record<string, any>) => void;
  stageIndex: number;
  applicationId: string;
  currentStep: number;
}

export const DynamicForm: React.FC<DynamicFormProps> = ({
  fields,
  formData,
  onFormDataChange,
  stageIndex,
  applicationId,
  currentStep,
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: [],
    hasChanges: false,
  });
  const [originalFormData, setOriginalFormData] = useState<Record<string, any>>(
    {}
  );
  const [showValidationErrors, setShowValidationErrors] = useState(false);

  // Helper function to get options from field (handles both formats)
  const getFieldOptions = useCallback(
    (field: IWorkflowFormField | IApplicationFormField): string[] => {
      // Check if it's the new format with fieldOptions
      if ("fieldOptions" in field) {
        const appField = field as IApplicationFormField;
        return appField.fieldOptions || [];
      }

      // Legacy format with options
      const workflowField = field as IWorkflowFormField;
      const options = workflowField.options || [];

      // Handle both array format and comma-separated string format
      return options.flatMap((option) =>
        typeof option === "string"
          ? option
              .split(",")
              .map((o) => o.trim())
              .filter((o) => o.length > 0)
          : [option]
      );
    },
    []
  );

  // Helper function to get initial form data with fieldValue support
  const getInitialFormData = useCallback((): Record<string, any> => {
    const initialData: Record<string, any> = { ...formData };

    // Populate form with existing fieldValue data from application fields
    fields.forEach((field) => {
      const fieldKey = field.fieldName;

      // Check if field has a fieldValue (from application data)
      if (
        "fieldValue" in field &&
        field.fieldValue !== undefined &&
        field.fieldValue !== null
      ) {
        const appField = field as IApplicationFormField;

        initialData[fieldKey] = appField.fieldValue;
      }
      // If no fieldValue but formData has a value, use that
      else if (formData[fieldKey] !== undefined) {
        initialData[fieldKey] = formData[fieldKey];
      }
      // Set default values based on field type
      else {
        switch (field.fieldType) {
          case "checkbox":
            initialData[fieldKey] = [];
            break;
          case "number":
            initialData[fieldKey] = "";
            break;
          default:
            initialData[fieldKey] = "";
        }
      }
    });

    return initialData;
  }, [fields, formData]);
  // Create dynamic schema based on fields - memoized to prevent recreation
  const schema = useMemo(() => {
    const schemaFields: Record<string, z.ZodTypeAny> = {};

    fields.forEach((field) => {
      let fieldSchema: z.ZodTypeAny;

      switch (field.fieldType) {
        case "text":
        case "textarea":
          fieldSchema = z.string();
          break;
        case "email":
          fieldSchema = z.string().email("Please enter a valid email address");
          break;
        case "tel":
          fieldSchema = z
            .string()
            .regex(
              /^[\+]?[0-9\s\-\(\)]+$/,
              "Please enter a valid phone number"
            );
          break;
        case "number":
          fieldSchema = z
            .string()
            .regex(/^\d+(\.\d+)?$/, "Please enter a valid number");
          break;
        case "checkbox":
          fieldSchema = z.array(z.string()).optional();
          break;
        case "radio":
        case "select":
          fieldSchema = z.string();
          break;
        case "date":
          fieldSchema = z.string().min(1, "Please select a date");
          break;
        default:
          fieldSchema = z.string();
      }

      // Apply required validation if needed
      if (field.required) {
        if (field.fieldType === "checkbox") {
          fieldSchema = (fieldSchema as z.ZodArray<any>).min(
            1,
            `${field.fieldName} is required`
          );
        } else {
          fieldSchema = (fieldSchema as z.ZodString).min(
            1,
            `${field.fieldName} is required`
          );
        }
      } else {
        if (field.fieldType !== "checkbox") {
          fieldSchema = fieldSchema.optional();
        }
      }

      schemaFields[field.fieldName] = fieldSchema;
    });

    return z.object(schemaFields);
  }, [fields]);
  type FormData = z.infer<typeof schema>;

  // Initialize form with proper default values
  const initialFormData = useMemo(
    () => getInitialFormData(),
    [getInitialFormData]
  );

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: initialFormData,
  });

  // Debounce timer ref
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  // Initialize original form data when component mounts
  useEffect(() => {
    const initialData = getInitialFormData();
    setOriginalFormData(initialData);
  }, [getInitialFormData]);

  // Validation function
  const validateCurrentForm = useCallback(() => {
    const currentFormData = form.getValues();
    const fieldConfigs = getFieldConfigs(fields);

    const result = validateForm(
      currentFormData,
      originalFormData,
      fieldConfigs
    );

    setValidationResult(result);
    return result;
  }, [form, fields, originalFormData]);

  // Memoized callback to prevent infinite re-renders with debouncing
  const handleFormChange = useCallback(
    (data: Record<string, any>) => {
      // Clear existing timer
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }

      // Set new timer to debounce rapid changes
      debounceTimer.current = setTimeout(() => {
        // Only call onFormDataChange if the data has actually changed
        const currentData = JSON.stringify(formData);
        const newData = JSON.stringify(data);

        if (currentData !== newData) {
          onFormDataChange(data);
        }

        // Validate form after changes
        validateCurrentForm();
      }, 300); // 300ms debounce
    },
    [formData, onFormDataChange, validateCurrentForm]
  );

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  // Function to save form data to the backend
  const handleSaveFormData = useCallback(async () => {
    // Validate form before submission
    const validation = validateCurrentForm();
    setShowValidationErrors(true);

    // Check for validation errors
    if (!validation.isValid) {
      toast.error("Form validation failed", {
        description: formatValidationErrors(validation.errors),
      });

      // Focus on first invalid field
      const firstError = validation.errors[0];
      if (firstError) {
        const fieldElement = document.querySelector(
          `[name="${firstError.field}"]`
        ) as HTMLElement;
        if (fieldElement) {
          fieldElement.focus();
        }
      }
      return;
    }

    // Check for changes
    if (!validation.hasChanges) {
      toast.info("No changes detected", {
        description: "Please make changes before submitting.",
      });
      return;
    }

    setIsSaving(true);
    try {
      // Get current form values
      const currentFormData = form.getValues();

      // Transform form data to match the required API structure
      const fields = Object.entries(currentFormData).map(
        ([fieldName, fieldValue]) => ({
          fieldName,
          fieldValue: fieldValue || "",
        })
      );

      const payload = {
        formData: [
          {
            stageOrder: stageIndex + 1, // Convert 0-based index to 1-based order
            fields,
          },
        ],
        currentStep: (currentStep + 1).toString(), // Convert to string as expected by API
      };

      // Debug payload structure for development
      // console.log(payload);

      const response = await fetch(`/api/applications/${applicationId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast.success("Form data saved successfully", {
          description: "Your form data has been saved to the application.",
        });
        // Also trigger the parent's form data change handler
        onFormDataChange(currentFormData);

        // Update original form data to reflect saved state
        setOriginalFormData(currentFormData);
        setShowValidationErrors(false);
      } else {
        const errorData = await response.json();
        toast.error("Failed to save form data", {
          description: errorData.message || "Please try again.",
        });
      }
    } catch (error) {
      console.error("Error saving form data:", error);
      toast.error("Failed to save form data", {
        description: "An unexpected error occurred. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  }, [
    form,
    stageIndex,
    currentStep,
    applicationId,
    onFormDataChange,
    validateCurrentForm,
  ]);

  // Update form when fields change to ensure fieldValue data is loaded
  useEffect(() => {
    const newFormData = getInitialFormData();

    form.reset(newFormData);
  }, [fields, getInitialFormData, form]);

  // Update form when external formData changes (but prevent loops)
  useEffect(() => {
    const currentValues = form.getValues();
    const hasChanges =
      JSON.stringify(currentValues) !== JSON.stringify(formData);

    if (hasChanges && Object.keys(formData).length > 0) {
      form.reset({ ...form.getValues(), ...formData });
    }
  }, [formData, form]);

  const renderField = (field: IWorkflowFormField | IApplicationFormField) => {
    const fieldKey = field.fieldName;

    switch (field.fieldType) {
      case "text":
      case "email":
      case "tel":
        return (
          <FormField
            key={fieldKey}
            control={form.control}
            name={fieldKey as any}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.fieldName}
                  {field.required && (
                    <span className="text-destructive ml-1">*</span>
                  )}
                </FormLabel>
                <FormControl>
                  <Input
                    type={field.fieldType}
                    placeholder={`Enter ${field.fieldName.toLowerCase()}`}
                    {...formField}
                    className={cn(
                      showValidationErrors &&
                        validationResult.errors.some(
                          (e) => e.field === field.fieldName
                        ) &&
                        "border-destructive focus-visible:ring-destructive"
                    )}
                    onChange={(e) => {
                      formField.onChange(e);
                      // Trigger debounced form change
                      handleFormChange(form.getValues());
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "number":
        return (
          <FormField
            key={fieldKey}
            control={form.control}
            name={fieldKey as any}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.fieldName}
                  {field.required && (
                    <span className="text-destructive ml-1">*</span>
                  )}
                </FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="any"
                    placeholder={`Enter ${field.fieldName.toLowerCase()}`}
                    {...formField}
                    className={cn(
                      showValidationErrors &&
                        validationResult.errors.some(
                          (e) => e.field === field.fieldName
                        ) &&
                        "border-destructive focus-visible:ring-destructive"
                    )}
                    onChange={(e) => {
                      formField.onChange(e.target.value);
                      // Trigger debounced form change
                      handleFormChange(form.getValues());
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "textarea":
        return (
          <FormField
            key={fieldKey}
            control={form.control}
            name={fieldKey as any}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.fieldName}
                  {field.required && (
                    <span className="text-destructive ml-1">*</span>
                  )}
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={`Enter ${field.fieldName.toLowerCase()}`}
                    {...formField}
                    onChange={(e) => {
                      formField.onChange(e);
                      // Trigger debounced form change
                      handleFormChange(form.getValues());
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "select":
        const selectOptions = getFieldOptions(field);
        return (
          <FormField
            key={fieldKey}
            control={form.control}
            name={fieldKey as any}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.fieldName}
                  {field.required && (
                    <span className="text-destructive ml-1">*</span>
                  )}
                </FormLabel>
                <Select
                  onValueChange={(value) => {
                    formField.onChange(value);
                    // Trigger debounced form change
                    handleFormChange(form.getValues());
                  }}
                  value={formField.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={`Select ${field.fieldName.toLowerCase()}`}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {selectOptions.map((option) => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "radio":
        const radioOptions = getFieldOptions(field);
        return (
          <FormField
            key={fieldKey}
            control={form.control}
            name={fieldKey as any}
            render={({ field: formField }) => (
              <FormItem className="space-y-3">
                <FormLabel>
                  {field.fieldName}
                  {field.required && (
                    <span className="text-destructive ml-1">*</span>
                  )}
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => {
                      formField.onChange(value);
                      // Trigger debounced form change
                      handleFormChange(form.getValues());
                    }}
                    value={formField.value}
                    className="flex flex-col space-y-1"
                  >
                    {radioOptions.map((option) => (
                      <div key={option} className="flex items-center space-x-2">
                        <RadioGroupItem
                          value={option}
                          id={`${fieldKey}-${option}`}
                        />
                        <label
                          htmlFor={`${fieldKey}-${option}`}
                          className="text-sm font-normal"
                        >
                          {option}
                        </label>
                      </div>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "checkbox":
        const checkboxOptions = getFieldOptions(field);
        return (
          <FormField
            key={fieldKey}
            control={form.control}
            name={fieldKey as any}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.fieldName}
                  {field.required && (
                    <span className="text-destructive ml-1">*</span>
                  )}
                </FormLabel>
                <FormControl>
                  <div className="space-y-2">
                    {checkboxOptions.map((option) => (
                      <div key={option} className="flex items-center space-x-2">
                        <Checkbox
                          id={`${fieldKey}-${option}`}
                          checked={
                            Array.isArray(formField.value)
                              ? formField.value.includes(option)
                              : false
                          }
                          onCheckedChange={(checked) => {
                            const currentValues = Array.isArray(formField.value)
                              ? formField.value
                              : [];
                            let newValues;

                            if (checked) {
                              newValues = [...currentValues, option];
                            } else {
                              newValues = currentValues.filter(
                                (value: string) => value !== option
                              );
                            }

                            formField.onChange(newValues);
                            handleFormChange(form.getValues());
                          }}
                        />
                        <label
                          htmlFor={`${fieldKey}-${option}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {option}
                        </label>
                      </div>
                    ))}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "date":
        return (
          <FormField
            key={fieldKey}
            control={form.control}
            name={fieldKey as any}
            render={({ field: formField }) => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  {field.fieldName}
                  {field.required && (
                    <span className="text-destructive ml-1">*</span>
                  )}
                </FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !formField.value && "text-muted-foreground"
                        )}
                      >
                        {formField.value ? (
                          format(new Date(formField.value), "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={
                        formField.value ? new Date(formField.value) : undefined
                      }
                      onSelect={(date) => {
                        const dateString = date
                          ? format(date, "yyyy-MM-dd")
                          : "";
                        formField.onChange(dateString);
                        handleFormChange(form.getValues());
                      }}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      default:
        return null;
    }
  };

  if (fields.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          No form fields configured for this stage.
        </p>
      </div>
    );
  }

  return (
    <Form {...form}>
      <div className="space-y-6">
        {/* Validation Error Display */}
        {showValidationErrors && validationResult.errors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {formatValidationErrors(validationResult.errors)}
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {fields.map((field) => renderField(field))}
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button
            type="button"
            onClick={handleSaveFormData}
            disabled={
              isSaving || (!validationResult.hasChanges && showValidationErrors)
            }
            className="flex items-center gap-2"
          >
            {isSaving ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            {isSaving ? "Saving..." : "Save Form"}
          </Button>
        </div>
      </div>
    </Form>
  );
};
