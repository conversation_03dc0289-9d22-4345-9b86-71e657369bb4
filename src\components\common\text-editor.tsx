import React from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import Blockquote from "@tiptap/extension-blockquote";
import { Button } from "@/components/ui/button";
import {
  BoldIcon,
  ItalicIcon,
  ListIcon,
  QuoteIcon,
  UnderlineIcon,
  Heading1Icon,
  Heading2Icon,
  Heading3Icon,
  PilcrowIcon,
} from "lucide-react";
import Heading from "@tiptap/extension-heading";
import Paragraph from "@tiptap/extension-paragraph";

export default function TextEditor({ field }: { field: any }) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      Paragraph,
      Blockquote.configure({
        HTMLAttributes: {
          class: "dark:text-white text-black",
        },
      }),
      Heading.configure({
        levels: [1, 2, 3],
        HTMLAttributes: {
          class: "dark:text-white text-black",
        },
      }),
    ],
    editorProps: {
      attributes: {
        class: `prose mt-6 min-h-[300px] p-2 focus:outline-none dark:text-white border max-w-full rounded-lg`,
      },
    },
    content: field.value,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      field.onChange(html);
    },
  });

  return (
    <div>
      <div className="flex gap-4 w-full items-center bg-[#f3f2f3] p-3 rounded-lg dark:bg-[#272b30]">
        <Button
          type="button"
          onClick={() => editor?.chain().focus().toggleBold().run()}
          className="font-bold"
        >
          <BoldIcon />
        </Button>
        <Button
          type="button"
          onClick={() => editor?.chain().focus().toggleItalic().run()}
          className="italic"
        >
          <ItalicIcon />
        </Button>
        <Button
          type="button"
          onClick={() => editor?.chain().focus().toggleUnderline().run()}
          className="underline"
        >
          <UnderlineIcon />
        </Button>
        <Button
          type="button"
          onClick={() => editor?.chain().focus().toggleBlockquote().run()}
        >
          <QuoteIcon />
        </Button>
        <Button
          type="button"
          onClick={() => editor?.chain().focus().toggleBulletList().run()}
        >
          <ListIcon />
        </Button>
        <Button
          type="button"
          onClick={() =>
            editor?.chain().focus().toggleHeading({ level: 1 }).run()
          }
        >
          <Heading1Icon />
        </Button>
        <Button
          type="button"
          onClick={() =>
            editor?.chain().focus().toggleHeading({ level: 2 }).run()
          }
        >
          <Heading2Icon />
        </Button>
        <Button
          type="button"
          onClick={() =>
            editor?.chain().focus().toggleHeading({ level: 3 }).run()
          }
        >
          <Heading3Icon />
        </Button>
        <Button
          type="button"
          onClick={() => editor?.chain().focus().setParagraph().run()}
        >
          <PilcrowIcon />
        </Button>
      </div>
      <div>
        <EditorContent editor={editor} />
      </div>
    </div>
  );
}
