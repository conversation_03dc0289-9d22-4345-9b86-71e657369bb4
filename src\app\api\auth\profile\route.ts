import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Extract user data from session
    const userData = session.user || {};

    // Determine tokenType based on the login provider
    let tokenType: "user" | "admin" | "agent" = "admin"; // Default to admin

    if ((session.backendTokens as any)?.provider === "agent-credentials") {
      tokenType = "agent";
    } else if ((session.backendTokens as any)?.provider === "credentials") {
      tokenType = "admin";
    }

    // Construct profile data from session
    const profileData = {
      id: userData.id || "unknown",
      name: userData.name || "Unknown User",
      email: userData.email || "<EMAIL>",
      tokenType: tokenType,
      role: tokenType,
      created_at: (userData as any).created_at,
      updated_at: (userData as any).updated_at,
    };

    return NextResponse.json({
      success: true,
      data: profileData,
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
