"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Plus } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { WorkflowMasterDataTable } from "@/components/workflow-master/workflow-master-datatable";
import { CreateWorkflowMasterDialog } from "@/components/workflow-master/create-workflow-master-dialog";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";
import { apiUrl } from "@/utils/urls";

interface WorkflowMasterPageProps {}

const WorkflowMasterPage: React.FC<WorkflowMasterPageProps> = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { handle401Error } = useAuthError<PERSON>and<PERSON>();

  const [workflows, setWorkflows] = useState<IWorkflowMaster[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState({
    search: "",
    is_active: undefined as boolean | undefined,
  });
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const fetchWorkflows = useCallback(
    async (page: number = 1, currentFilters = filters) => {
      if (status === "loading") return;
      if (!session) {
        router.push("/signin");
        return;
      }

      setLoading(true);
      try {
        const queryParams = new URLSearchParams({
          page: page.toString(),
          limit: pagination.limit.toString(),
          ...(currentFilters.search && { search: currentFilters.search }),
          ...(currentFilters.is_active !== undefined && {
            is_active: currentFilters.is_active.toString(),
          }),
        });

        const response = await fetch(
          `${apiUrl}/workflow-master?${queryParams}`,
          {
            headers: {
              Authorization: `Bearer ${session?.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );
        const result = await response.json();

        if (response.ok) {
          setWorkflows(result.data || []);
          setPagination({
            page: result.page || page,
            limit: result.limit || 10,
            total: result.total || 0,
            totalPages: result.totalPages || 0,
          });
        } else {
          if (response.status === 401) {
            handle401Error();
            return;
          }
          toast.error(result.message || "Failed to fetch workflow masters");
          setWorkflows([]);
        }
      } catch (error) {
        console.error("Error fetching workflow masters:", error);
        toast.error("Failed to fetch workflow masters");
        setWorkflows([]);
      } finally {
        setLoading(false);
      }
    },
    [session, status, router, pagination.limit, filters, handle401Error]
  );

  useEffect(() => {
    fetchWorkflows(1, filters);
  }, [filters]);

  const handlePageChange = (page: number) => {
    fetchWorkflows(page, filters);
  };

  const handleSearch = (search: string) => {
    setFilters((prev) => ({ ...prev, search }));
  };

  const handleActiveFilter = (isActive: boolean | undefined) => {
    setFilters((prev) => ({ ...prev, is_active: isActive }));
  };

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
    fetchWorkflows(1, filters);
  };

  const handleUpdateSuccess = () => {
    fetchWorkflows(pagination.page, filters);
  };

  const handleDeleteSuccess = () => {
    fetchWorkflows(pagination.page, filters);
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workflow Master</h1>
          <p className="text-muted-foreground">
            Manage workflow templates and processes
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Workflow Master
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Workflow Master Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <WorkflowMasterDataTable
            data={workflows}
            loading={loading}
            pagination={pagination}
            onPageChange={handlePageChange}
            onSearch={handleSearch}
            onActiveFilter={handleActiveFilter}
            onUpdateSuccess={handleUpdateSuccess}
            onDeleteSuccess={handleDeleteSuccess}
          />
        </CardContent>
      </Card>

      <CreateWorkflowMasterDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
};

export default WorkflowMasterPage;
