"use client";

import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { apiUrl } from "@/utils/urls";

interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  serviceType?: string;
  serviceId?: string;
}

interface WorkflowTemplateSelectorProps {
  applicationId: string;
  currentWorkflowTemplateId?: string;
  currentWorkflowTemplateName?: string;
  onUpdate?: () => void;
}

export const WorkflowTemplateSelector: React.FC<WorkflowTemplateSelectorProps> = ({
  applicationId,
  currentWorkflowTemplateId,
  currentWorkflowTemplateName,
  onUpdate,
}) => {
  const { data: session } = useSession();
  const [workflowTemplates, setWorkflowTemplates] = useState<WorkflowTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);

  // Fetch immigration-related workflow templates
  useEffect(() => {
    const fetchWorkflowTemplates = async () => {
      if (!session?.backendTokens?.accessToken) return;

      setLoading(true);
      try {
        // Fetch workflow templates filtered for immigration services
        const response = await fetch(
          `${apiUrl}/workflow-templates?serviceId=cmcp500ap0000istocjrscl50p0000istocjrscl50&limit=100`,
          {
            headers: {
              Authorization: `Bearer ${session.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          setWorkflowTemplates(data.data || []);
        } else {
          console.error("Failed to fetch workflow templates");
        }
      } catch (error) {
        console.error("Error fetching workflow templates:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflowTemplates();
  }, [session]);

  const handleWorkflowTemplateChange = async (newWorkflowTemplateId: string) => {
    if (!session?.backendTokens?.accessToken || updating) return;

    setUpdating(true);
    try {
      const response = await fetch(`${apiUrl}/applications/${applicationId}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          workflow_template_id: newWorkflowTemplateId,
        }),
      });

      if (response.ok) {
        toast.success("Workflow template updated successfully", {
          description: "The application workflow template has been changed",
        });
        onUpdate?.();
      } else {
        const errorData = await response.json();
        toast.error("Failed to update workflow template", {
          description: errorData.message || "Please try again later",
        });
      }
    } catch (error) {
      console.error("Error updating workflow template:", error);
      toast.error("Failed to update workflow template", {
        description: "Please try again later",
      });
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="text-sm text-muted-foreground">Loading templates...</div>
    );
  }

  return (
    <div className="w-[200px]">
      <Select
        value={currentWorkflowTemplateId || ""}
        onValueChange={handleWorkflowTemplateChange}
        disabled={updating}
      >
        <SelectTrigger className="h-8 text-xs">
          <SelectValue 
            placeholder="Select template"
            className="text-xs"
          >
            {currentWorkflowTemplateName || "Select template"}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {workflowTemplates.map((template) => (
            <SelectItem key={template.id} value={template.id}>
              <div>
                <div className="font-medium text-xs">{template.name}</div>
                {template.description && (
                  <div className="text-xs text-muted-foreground truncate max-w-[150px]">
                    {template.description}
                  </div>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
