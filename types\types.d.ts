/* eslint-disable no-unused-vars */
interface IMentor {
  id?: string;
  name: string;
  email: string;
  image: string;
  desc: string;
  order?: number;
  password?: string;
  linkedin?: string;
  profile?: string;
  designation: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface User {
  id: string;
  name: string;
  email: string;
  image: string | null;
}

interface ServiceUser {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  user: User;
}

interface MentorInfoService {
  id: string;
  createdAt: string;
  description: string;
  meeting_link: string;
  name: string;
  price: number;
  updatedAt: string;
  status: string;
  users: ServiceUser[];
}

interface IMentorInfo {
  id: string;
  name: string;
  email: string;
  image: string;
  location: string;
  designation: string;
  desc: string;
  status: string;
  total_revenue: string;
  reviews: IReview[];
  services: MentorInfoService[];
  createdAt: Date;
  updatedAt: Date;
}

interface IService {
  id?: string;
  name: string;
  price: number;
  description: string;
  meeting_link: string;
  status?: Status;
  createdAt?: Date;
  updatedAt?: Date;
}

interface IReview {
  id: string;
  message: string;
  rating: number;
  mentor: IMentor;
  user: IUser;
  createdAt: Date;
  updatedAt: Date;
}

interface Mentor {
  id: string;
  name: string;
  image: string;
}

interface MentorService {
  price: number;
  name: string;
  id: string;
  meeting_link: string;
  mentor: Mentor;
}

interface Service {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  mentor_services: MentorService;
}

interface Package {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  package: {
    amount: number;
    name: string;
    id: string;
  };
}
interface Training {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  training: {
    amount: number;
    name: string;
    id: string;
  };
}

interface ImmigrationService {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: string;
  immigration_service: {
    amount: number;
    name: string;
    id: string;
  };
}

interface IUser {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  password?: string | null;
  createdAt: Date;
  updatedAt: Date;
  provider: string;
  total_spent: string;
  reviews: IReview[];
  services: Service[];
  packages: Package[];
  training: Training[];
  immigration_services: ImmigrationService[];
}

interface IContactUs {
  id: string;
  name: string;
  email: string;
  mobile: string;
  message: string;
  createdAt: Date;
  updatedAt: Date;
}

interface IBlog {
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  desc: string;
  title: string;
  slug?: string;
  summary: string;
  blogger: string;
  img: string;
}

interface IPackage {
  id?: string;
  name: string;
  note: string;
  amount: number;
  order?: number;
  service: string[];
  createdAt?: Date;
  updatedAt?: Date;
}
interface IImmigration {
  id?: string;
  name: string;
  amount: number;
  order?: number;
  service: string[];
  website_visible?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}
interface ITraining {
  id?: string;
  name: string;
  amount: number;
  img: string;
  order?: number;
  service: string[];
  highlights: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

interface Mentor {
  id: string;
  name: string;
  image: string;
  order?: number;
  average_rating: number;
  review_count: string;
  revenue_generated: string;
  total_clients: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  image: string | null;
  createdAt: string;
}

interface Contact {
  id: string;
  name: string;
  email: string;
  message: string;
  createdAt: string;
}

interface IDashboard {
  total_mentors: string;
  total_users: string;
  mentor_service_revenue: string;
  package_revenue: string;
  immigration_service_revenue: string;
  training_revenue: string;
  total_revenue: string;
  top_rated_mentors: Mentor[];
  latest_users: User[];
  latest_contacts: Contact[];
}

interface IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
}
interface IGuestService extends IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
  mentor_services: MentorService;
}
interface IGuestImmigration extends IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
  immigration_service: {
    amount: number;
    name: string;
    id: string;
  };
}
interface IGuestTraining extends IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
  training: {
    amount: number;
    name: string;
    id: string;
  };
}
interface IGuestPackage extends IGuest {
  id: string;
  amount: number;
  status: string;
  progress: string;
  createdAt: Date;
  package: {
    amount: number;
    name: string;
    id: string;
  };
}

interface ICustomerReview {
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  name: string;
  img?: string;
  comment: string;
  source: string;
  rating: number;
  order?: number;
  date: Date;
}

interface IComment {
  id: string;
  content: string;
  blogId: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
  parentId: string | null;
  author: {
    name: string;
    image: string | null;
  };
  replies?: IComment[];
}

// Payment System Types (v2 API)
interface PaymentData {
  id: string;
  amount: number;
  status: "pending" | "paid" | "failed";
  payment_type: "user" | "guest";
  service_type: "service" | "package" | "immigration" | "training";
  progress: string;
  stripe_session_id: string;
  stripe_payment_intent_id?: string;
  payment_method?: string;
  transaction_id?: string;
  createdAt: string;
  updatedAt: string;

  // Service ID fields (one will be populated based on serviceType)
  userId?: string | null;
  serviceId?: string | null;
  packageId?: string | null;
  immigration_serviceId?: string | null;
  trainingId?: string | null;

  // User payments
  user?: {
    id: string;
    name: string;
    email: string;
    image?: string;
  } | null;

  // Guest payments
  guest_name?: string;
  guest_email?: string;
  guest_mobile?: string;

  // Service relationships
  service?: {
    id: string;
    name: string;
    amount: number;
    description?: string;
    mentorId?: string;
    mentor?: {
      id: string;
      name: string;
      email: string;
    };
  } | null;
  package?: {
    id: string;
    name: string;
    amount: number;
    description?: string;
    features?: string[];
  } | null;
  immigration_service?: {
    id: string;
    name: string;
    amount: number;
    description?: string;
    processing_time?: string;
    requirements?: string[];
  } | null;
  training?: {
    id: string;
    name: string;
    amount: number;
    description?: string;
    duration?: string;
    level?: "Beginner" | "Intermediate" | "Advanced";
  } | null;

  // Application integration
  application_id?: string;
}

// Payment Creation Types (v2 API)
interface CreatePaymentRequest {
  amount: number;
  user_id: string;
  serviceType: "immigration";
  serviceId: string;
  discount_amount: number; // Always required as integer (at least 0)
  actual_amount: number;
  payment_method: "stripe" | "cash" | "bank_deposit" | "online_transfer";
  transactionId?: string; // Optional for all payment methods
}

interface CreatePaymentResponse {
  success: boolean;
  payment_id: string;
  stripe_link?: string; // Only present for Stripe payments
  message?: string;
}

interface PaymentError {
  success: false;
  message: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

interface PaymentHistoryResponse {
  page: number;
  limit: number;
  totalPages: number;
  totalItems: number;
  data: PaymentData[];
}

interface PaymentProgressUpdateRequest {
  progress:
    | "Accepted"
    | "Rejected"
    | "Pending"
    | "Completed"
    | "Active"
    | "Inactive"
    | "Blocked"
    | "Cancelled";
}

interface PaymentProgressUpdateResponse {
  success: boolean;
  message: string;
  data: PaymentData;
}

// Immigration Document Master Types
interface IDocumentType {
  id: string;
  name: string;
  description?: string;
}

interface IDocumentMaster {
  id?: string;
  name: string;
  description?: string;
  category: string;
  instructions?: string;
  created_by?: string;
  created_at?: string;
  updated_at?: string;
}

// Legacy interface for backward compatibility
interface IImmigrationDocument extends IDocumentMaster {
  document_type: IDocumentType;
}

// Workflow Master Types
interface IWorkflowMaster {
  id?: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_by?: string;
  updated_by?: string | null;
  created_at?: string;
  updated_at?: string;
}

// Workflow Template Types
interface IWorkflowFormField {
  fieldName: string;
  fieldType: string;
  required: boolean;
  options?: string[]; // For select and checkbox field types
  showToClient: boolean; // Controls visibility on client-facing forms
}

// Application-specific form field interface (matches application_data.json structure)
interface IApplicationFormField {
  id: string;
  fieldName: string;
  fieldType: string;
  required: boolean;
  fieldValue?: any; // Stores user-submitted data for the field
  fieldOptions: string[]; // For select and checkbox field types
  showToClient: boolean; // Controls visibility on client-facing forms
}

interface IWorkflowDocument {
  documentName: string;
  required: boolean;
}

// Application-specific document interface (matches application_data.json structure)
interface IApplicationDocumentData {
  id: string;
  fileName: string;
  fileUrl: string;
  required: boolean;
  status: string;
  requestReason: string;
  uploadDate: string;
  updatedAt: string;
}

// Notification System Types
interface INotificationSetting {
  id?: string;
  userId?: string;
  userType: "admin" | "agent" | "client";
  notificationType: NotificationType;
  channels: NotificationChannel[];
  isEnabled: boolean;
  customSchedule?: number; // Custom schedule in hours for reminder types
  created_at?: string;
  updated_at?: string;
}

type NotificationType =
  | "document_reminder"
  | "application_submitted"
  | "agent_assigned"
  | "status_update"
  | "agent_query"
  | "document_rejected"
  | "authority_query"
  | "deadline_warning"
  | "missing_document"
  | "eligibility_confirmation"
  | "payment_confirmation"
  | "final_decision"
  | "system_maintenance"
  | "escalation_notice";

type NotificationChannel = "email" | "sms" | "in_app";

interface INotificationSettingsResponse {
  data: INotificationSetting[];
}

interface IWorkflowStage {
  stageName: string;
  stageOrder: number;
  documentsRequired: boolean;
  documents: IWorkflowDocument[] | IApplicationDocumentData[];
  customFormRequired: boolean;
  customForm: IWorkflowFormField[] | IApplicationFormField[];
  showToClient?: boolean; // Controls stage visibility on client-facing forms
}

// Application-specific step interface (matches application_data.json structure)
interface IApplicationStep {
  stageOrder: number;
  stageName: string;
  documentsRequired: boolean;
  customFormRequired: boolean;
  documents: IApplicationDocumentData[];
  customForm: IApplicationFormField[];
}

interface IWorkflowTemplate {
  id?: string;
  name: string;
  description?: string;
  serviceType?: string;
  serviceId?: string;
  packageName?: string; // Immigration package name for display
  isActive: boolean;
  isDefault?: boolean; // Whether this is the default template for the service
  workflowTemplate: IWorkflowStage[];
  createdBy?: string;
  updatedBy?: string | null;
  createdAt?: string;
  updatedAt?: string;
}

// Application Management Types
interface IApplicationGuest {
  name: string;
  email: string;
  mobile: string;
}

// Application user interface (matches application_data.json structure)
interface IApplicationUser {
  id: string;
  name: string;
  email: string;
}

interface IApplication {
  id: string;
  application_number: string;
  service_type: string;
  service_id?: string; // Service ID for dynamic workflow template filtering
  service_name?: string; // Service name for display in data table
  status: string;
  priority_level: string;
  current_step: number;
  numberOfSteps?: number; // Total number of steps in the workflow
  guest?: IApplicationGuest; // Made optional for backward compatibility
  user?: IApplicationUser; // Added to support new data structure
  steps: any[];
  estimated_completion: string | null;
  assigned_agent?: IAgent | IAgent[]; // Legacy agent assignment information - supports single or multiple agents
  agent_ids?: IAgent[]; // New API format - array of agent objects with id, name, and email
  payments?: string[]; // Array of payment IDs
  workflow_template?: { // Workflow template information for display
    id: string;
    name: string;
    description?: string;
  };
  created_at: string;
  updated_at: string;
}

// Application data interface (matches application_data.json structure)
interface IApplicationData {
  id: string;
  application_number: string;
  service_type: string;
  status: string;
  priority_level: string;
  current_step: string;
  workflow_template: {
    id: string;
    name: string;
    description: string;
  };
  steps: IApplicationStep[];
  estimated_completion: string | null;
  created_at: string;
  updated_at: string;
  user: IApplicationUser;
}

// Detailed Application Interface for individual application view
interface IApplicationDetail extends IApplication {
  workflow_template?: IWorkflowTemplate;
  form_data?: Record<string, any>;
  uploaded_documents?: IApplicationDocument[];
  notes?: string;
  assigned_to?: string;
}

interface IApplicationDocument {
  id: string;
  document_name: string;
  file_url: string;
  stage_name: string;
  uploaded_at: string;
  uploaded_by: string;
  status?: DocumentStatus;
  reason?: string;
}

// Document Status Enum
enum DocumentStatus {
  PENDING = "Pending",
  UNDER_REVIEW = "Under_Review",
  APPROVED = "Approved",
  REJECTED = "Rejected",
  REQUIRED_REVISION = "Required_Revision",
}

// Document Status Update Request Interface
interface DocumentStatusUpdateRequest {
  status: DocumentStatus;
  reason?: string; // Required when status is REJECTED, REQUIRED_REVISION, EXPIRED, or UNDER_REVIEW
}

// Document Request Interface
interface DocumentRequestBody {
  documentName: string;
  reason: string;
  documentCategory: string;
  stageOrder: number;
  required: boolean;
}

interface IApplicationsResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  data: IApplication[];
}

// Agent Management Types
interface IAgent {
  id?: string;
  name: string;
  email: string;
  phone: string;
  status: "Active" | "Inactive" | "Blocked";
  created_at?: string;
  updated_at?: string;
  created_by_admin?: string;
}

interface IAgentsResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  data: IAgent[];
}

// Profile Types
interface IProfile {
  id: string;
  name: string;
  email: string;
  tokenType: "user" | "admin" | "agent";
  role?: string;
  created_at?: string;
  updated_at?: string;
}

// Create New Application Types
interface ICreateUser {
  name: string;
  email: string;
  phone: string;
}

interface ICreateImmigrationProduct {
  name: string;
  description?: string;
  price: number;
  category: string;
}

interface ICreateApplicationFormData {
  // User Selection/Creation
  userId?: string;
  createNewUser: boolean;
  newUser?: ICreateUser;

  // Immigration Product Selection/Creation
  immigrationProductId?: string;
  createNewProduct: boolean;
  newProduct?: ICreateImmigrationProduct;

  // Discount Application
  originalPrice: number;
  discountAmount?: number;
  discountedPrice: number;

  // Workflow Template Selection/Creation
  workflowTemplateId: string;
  createNewTemplate: boolean;

  // Agent Assignment (Optional) - supports multiple agents
  assignedAgentId?: string | string[];

  // Payment IDs (Optional) - supports multiple payments
  payments?: string[];

  // Application Metadata
  priorityLevel: "Low" | "Medium" | "High";
  notes?: string;
}

interface ICreateApplicationRequest {
  user_id?: string;
  service_id?: string;
  workflow_template_id: string;
  assigned_agent?: string[]; // Changed to array and renamed for consistency
  payments?: string[]; // Array of payment IDs
  original_price?: number;
  discounted_price?: number;
  discount_amount?: number;
  priority_level: string;
  notes?: string;
  service_type: string;
  status?: string;
  current_step?: number;
}

interface ICreateApplicationResponse {
  success: boolean;
  data?: IApplication;
  message?: string;
}
