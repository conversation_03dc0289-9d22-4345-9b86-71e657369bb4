"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Plus, X, ArrowLeft, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { createWorkflowTemplateSchema } from "@/utils/schema";
import { useCreateWorkflowTemplate } from "@/hooks/use-query";
import { apiUrl } from "@/utils/urls";
import { DocumentsSection } from "@/components/workflow-templates/documents-section";
import { CustomFormSection } from "@/components/workflow-templates/custom-form-section";

type FormData = {
  name: string;
  description: string;
  immigrationPackageId: string;
  isActive: boolean;
  workflowTemplate: {
    stageName: string;
    stageOrder: number;
    documentsRequired: boolean;
    documents: { documentName: string; required: boolean }[];
    customFormRequired: boolean;
    customForm: {
      fieldName: string;
      fieldType: string;
      required: boolean;
      options: string[];
      showToClient: boolean;
    }[];
    showToClient: boolean; // Controls stage visibility on client-facing forms
  }[];
};

const CreateWorkflowTemplatePage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const createMutation = useCreateWorkflowTemplate();

  const [workflowMasters, setWorkflowMasters] = useState<IWorkflowMaster[]>([]);
  const [documentMasters, setDocumentMasters] = useState<IDocumentMaster[]>([]);
  const [immigrationPackages, setImmigrationPackages] = useState<
    IImmigration[]
  >([]);
  const [loadingMasters, setLoadingMasters] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(createWorkflowTemplateSchema),
    defaultValues: {
      name: "",
      description: "",
      immigrationPackageId: "",
      isActive: true,
      workflowTemplate: [
        {
          stageName: "",
          stageOrder: 1,
          documentsRequired: false,
          documents: [],
          customFormRequired: false,
          customForm: [],
          showToClient: true,
        },
      ],
    },
  });

  const {
    fields: stageFields,
    append: appendStage,
    remove: removeStage,
  } = useFieldArray({
    control: form.control,
    name: "workflowTemplate",
  });

  // Load master data
  useEffect(() => {
    const loadMasterData = async () => {
      if (!session?.backendTokens?.accessToken) return;

      try {
        setLoadingMasters(true);

        // Load immigration packages
        const immigrationResponse = await fetch(`${apiUrl}/immigration`, {
          headers: {
            Authorization: `Bearer ${session.backendTokens.accessToken}`,
            "Content-Type": "application/json",
          },
        });

        if (immigrationResponse.ok) {
          const immigrationData = await immigrationResponse.json();
          setImmigrationPackages(immigrationData || []);
        }

        // Load workflow masters (with high limit to get all)
        const workflowResponse = await fetch(
          `${apiUrl}/workflow-master?page=1&limit=100`,
          {
            headers: {
              Authorization: `Bearer ${session.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (workflowResponse.ok) {
          const workflowData = await workflowResponse.json();
          setWorkflowMasters(workflowData.data || []);
        }

        // Load document masters (with high limit to get all)
        const documentResponse = await fetch(
          `${apiUrl}/document-master?page=1&limit=100`,
          {
            headers: {
              Authorization: `Bearer ${session.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (documentResponse.ok) {
          const documentData = await documentResponse.json();
          setDocumentMasters(documentData.data || []);
        }
      } catch (error) {
        console.error("Failed to load master data:", error);
      } finally {
        setLoadingMasters(false);
      }
    };

    if (session) {
      loadMasterData();
    }
  }, [session]);

  const onSubmit = async (data: FormData) => {
    try {
      // Clean up the data before submission
      const cleanedData = {
        name: data.name,
        description: data.description,
        serviceType: "immigration", // Always set to immigration
        serviceId: data.immigrationPackageId, // Use immigration package ID as service ID
        isActive: data.isActive,
        workflowTemplate: data.workflowTemplate.map((stage) => ({
          stageName: stage.stageName,
          stageOrder: stage.stageOrder,
          documentsRequired: stage.documentsRequired,
          documents: stage.documents || [],
          customFormRequired: stage.customFormRequired, // Fixed typo from "cutomFormRequired"
          customForm: (stage.customForm || []).map((field) => ({
            ...field,
            showToClient:
              field.showToClient !== undefined ? field.showToClient : true,
          })),
          showToClient:
            stage.showToClient !== undefined ? stage.showToClient : true, // Include showToClient in submission
        })),
      };

      await createMutation.mutateAsync(cleanedData);

      toast.success("Workflow template created successfully", {
        description: "The workflow template has been added and is ready to use",
      });

      // Redirect back to workflow templates page
      router.push("/workflow-templates");
    } catch (error) {
      console.error("Error creating workflow template:", error);

      // Provide user-friendly error message
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while creating the workflow template";

      toast.error("Failed to create workflow template", {
        description: errorMessage,
      });
    }
  };

  const addStage = () => {
    const newStageOrder = stageFields.length + 1;
    appendStage({
      stageName: "",
      stageOrder: newStageOrder,
      documentsRequired: false,
      documents: [],
      customFormRequired: false,
      customForm: [],
      showToClient: true, // Default to visible
    });
  };

  // Helper function to get available workflow masters (excluding already selected ones)
  const getAvailableWorkflowMasters = (currentIndex: number) => {
    const selectedStageNames = form
      .getValues("workflowTemplate")
      .map((stage, index) => (index !== currentIndex ? stage.stageName : null))
      .filter(Boolean);

    return workflowMasters.filter(
      (workflow) =>
        workflow.is_active && !selectedStageNames.includes(workflow.name)
    );
  };

  // Helper function to get available document masters (excluding already selected ones across all stages)
  const getAvailableDocumentMasters = (
    currentStageIndex: number,
    currentDocIndex: number
  ) => {
    const allSelectedDocuments: string[] = [];

    form.getValues("workflowTemplate").forEach((stage, stageIndex) => {
      stage.documents.forEach((doc, docIndex) => {
        if (
          !(stageIndex === currentStageIndex && docIndex === currentDocIndex)
        ) {
          allSelectedDocuments.push(doc.documentName);
        }
      });
    });

    return documentMasters.filter(
      (doc) => !allSelectedDocuments.includes(doc.name)
    );
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push("/workflow-templates")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Workflow Templates
        </Button>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Create Workflow Template
          </h1>
          <p className="text-muted-foreground">
            Create a new workflow template for immigration services
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card className="p-6">
            <div className="flex items-center gap-2 pb-4 border-b">
              <div className="h-2 w-2 bg-primary rounded-full"></div>
              <h3 className="text-lg font-semibold">Basic Information</h3>
            </div>
            <div className="mt-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter template name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="immigrationPackageId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Immigration Package *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                loadingMasters
                                  ? "Loading packages..."
                                  : "Select immigration package"
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {immigrationPackages.map((pkg) => (
                            <SelectItem key={pkg.id} value={pkg.id!}>
                              {pkg.name} - €{pkg.amount}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter template description"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Enable this template for use
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </Card>

          {/* Workflow Stages Section */}
          <div className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center justify-between pb-4 border-b">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 bg-primary rounded-full"></div>
                  <h3 className="text-lg font-semibold">Workflow Stages</h3>
                </div>
              </div>
              <div className="mt-6 space-y-6">
                {stageFields.map((stage, stageIndex) => (
                  <Card key={stage.id} className="relative">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">
                          Stage {stageIndex + 1}
                        </CardTitle>
                        {stageFields.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeStage(stageIndex)}
                            className="text-destructive hover:text-destructive"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name={`workflowTemplate.${stageIndex}.stageName`}
                          render={({ field }) => {
                            const availableWorkflows =
                              getAvailableWorkflowMasters(stageIndex);
                            return (
                              <FormItem>
                                <FormLabel>Stage Name *</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                  disabled={loadingMasters}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue
                                        placeholder={
                                          loadingMasters
                                            ? "Loading workflows..."
                                            : "Select workflow stage"
                                        }
                                      />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {availableWorkflows.map((workflow) => (
                                      <SelectItem
                                        key={workflow.id}
                                        value={workflow.name}
                                      >
                                        {workflow.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            );
                          }}
                        />

                        <FormField
                          control={form.control}
                          name={`workflowTemplate.${stageIndex}.stageOrder`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Stage Order *</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(parseInt(e.target.value))
                                  }
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Stage Visibility Toggle */}
                      <FormField
                        control={form.control}
                        name={`workflowTemplate.${stageIndex}.showToClient`}
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <div className="space-y-0.5">
                              <FormLabel className="text-sm">
                                Show to Client
                              </FormLabel>
                              <div className="text-xs text-muted-foreground">
                                Controls whether this stage is visible to
                                clients
                              </div>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name={`workflowTemplate.${stageIndex}.documentsRequired`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel className="text-sm">
                                  Documents Required
                                </FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`workflowTemplate.${stageIndex}.customFormRequired`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel className="text-sm">
                                  Custom Form Required
                                </FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Documents Section */}
                      {form.watch(
                        `workflowTemplate.${stageIndex}.documentsRequired`
                      ) && (
                        <DocumentsSection
                          stageIndex={stageIndex}
                          form={form}
                          documentMasters={documentMasters}
                          loadingMasters={loadingMasters}
                          getAvailableDocumentMasters={
                            getAvailableDocumentMasters
                          }
                        />
                      )}

                      {/* Custom Form Section */}
                      {form.watch(
                        `workflowTemplate.${stageIndex}.customFormRequired`
                      ) && (
                        <CustomFormSection
                          stageIndex={stageIndex}
                          form={form}
                        />
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Add Stage Button - Made more prominent */}
              <div className="mt-6 pt-4 border-t">
                <Button
                  type="button"
                  onClick={addStage}
                  size="lg"
                  variant="outline"
                  className="w-full font-bold text-lg py-3"
                >
                  <Plus className="mr-2 h-5 w-5" />
                  Add Stage
                </Button>
              </div>
            </Card>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/workflow-templates")}
              disabled={createMutation.isPending}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={createMutation.isPending}>
              {createMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {createMutation.isPending
                ? "Creating Template..."
                : "Create Template"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CreateWorkflowTemplatePage;
