import { Mail } from "lucide-react";
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { formatDate } from "@/utils/tools";

const Contact = ({ contacts }: { contacts: Contact[] }) => {
  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle>Recent Enquiries</CardTitle>
        <CardDescription>Latest contact form submissions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {contacts.map((contact) => (
            <div key={contact.id} className="flex items-center">
              <Mail className="h-9 w-9 p-2 rounded-full bg-secondary" />
              <div className="ml-4 space-y-1">
                <p className="text-sm font-medium leading-none">
                  {contact.name}
                </p>
                <p className="text-sm text-muted-foreground">{contact.email}</p>
              </div>
              <div className="ml-auto text-sm text-muted-foreground">
                {formatDate(contact.createdAt)}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default Contact;
