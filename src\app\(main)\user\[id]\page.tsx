import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { getUser } from "@/hooks/use-server";
import Detail from "@/components/user/detail";
import Reviews from "@/components/user/reviews";
import MentorService from "@/components/user/mentor-service";
import Packages from "@/components/user/packages";
import Immigration from "@/components/user/immigration";
import { Card, CardTitle } from "@/components/ui/card";
import Training from "@/components/user/training";

const UserProfilePage = async ({ params }: { params: { id: string } }) => {
  const data = await getUser(params.id);
  return (
    <ContentLayout title="User">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link href="/user">Users</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{data?.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <Content>
        {data && <Detail data={data} />}
        {data && <Reviews data={data} />}
        {data && <MentorService data={data.services} />}
        {data && <Packages data={data.packages} />}
        {data && <Immigration data={data.immigration_services} />}
        {data && <Training data={data.training} />}
        <Card>
          <div className="flex items-center justify-between p-6">
            <CardTitle className="text-lg font-medium text-gray-700">
              Total Amount Spent
            </CardTitle>
            <div className="text-3xl font-bold text-primary">
              €{data?.total_spent}
            </div>
          </div>
        </Card>
      </Content>
    </ContentLayout>
  );
};

export default UserProfilePage;
