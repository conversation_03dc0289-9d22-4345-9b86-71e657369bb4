"use client";

import React from "react";
import { useFieldArray } from "react-hook-form";
import { Plus, X } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

interface DocumentsSectionProps {
  stageIndex: number;
  form: any;
  documentMasters: IDocumentMaster[];
  loadingMasters: boolean;
  getAvailableDocumentMasters: (
    stageIndex: number,
    docIndex: number
  ) => IDocumentMaster[];
}

export const DocumentsSection: React.FC<DocumentsSectionProps> = ({
  stageIndex,
  form,
  documentMasters,
  loadingMasters,
  getAvailableDocumentMasters,
}) => {
  const {
    fields: documentFields,
    append: appendDocument,
    remove: removeDocument,
  } = useFieldArray({
    control: form.control,
    name: `workflowTemplate.${stageIndex}.documents`,
  });

  const addDocument = () => {
    appendDocument({ documentName: "", required: false });
  };

  return (
    <Card className="mt-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Required Documents</CardTitle>
          <Button
            type="button"
            onClick={addDocument}
            size="sm"
            variant="outline"
          >
            <Plus className="mr-2 h-3 w-3" />
            Add Document
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {documentFields.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-4">
            No documents added yet. Click &quot;Add Document&quot; to get
            started.
          </p>
        ) : (
          documentFields.map((document, docIndex) => {
            const availableDocuments = getAvailableDocumentMasters(
              stageIndex,
              docIndex
            );
            return (
              <div
                key={document.id}
                className="flex items-end gap-4 p-4 border rounded-lg"
              >
                <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`workflowTemplate.${stageIndex}.documents.${docIndex}.documentName`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Name *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                          disabled={loadingMasters}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder={
                                  loadingMasters
                                    ? "Loading documents..."
                                    : "Select document"
                                }
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {availableDocuments.map((doc) => (
                              <SelectItem key={doc.id} value={doc.name}>
                                {doc.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`workflowTemplate.${stageIndex}.documents.${docIndex}.required`}
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel className="text-sm">Required</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeDocument(docIndex)}
                  className="text-destructive hover:text-destructive"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            );
          })
        )}
      </CardContent>
    </Card>
  );
};
