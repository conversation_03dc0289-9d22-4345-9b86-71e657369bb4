"use client";

import React, { useCallback } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { DynamicForm } from "./dynamic-form";
import { DocumentManager } from "./document-manager";

import { Badge } from "@/components/ui/badge";
import { FileText, FormInput, CheckCircle, AlertCircle } from "lucide-react";
import { validateDocumentRequirements } from "@/utils/form-validation";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Helper function to normalize document data
const normalizeDocument = (
  doc: IWorkflowDocument | IApplicationDocumentData
): { name: string; required: boolean; id?: string } => {
  if ("documentName" in doc) {
    // Legacy format
    return { name: doc.documentName, required: doc.required };
  } else {
    // New format
    return { name: doc.fileName, required: doc.required, id: doc.id };
  }
};

interface StageManagerProps {
  stage: IWorkflowStage;
  stageIndex: number;
  formData: Record<string, any>;
  uploadedDocuments: IApplicationDocument[];
  onFormDataChange: (stageData: Record<string, any>) => void;
  onDocumentUpload: (documents: IApplicationDocument[]) => void;
  onDocumentStatusUpdate?: (
    documentId: string,
    newStatus: DocumentStatus,
    reason?: string
  ) => void;
  applicationId: string;
  currentStep: number;
}

const StageManagerComponent: React.FC<StageManagerProps> = ({
  stage,
  stageIndex,
  formData,
  uploadedDocuments,
  onFormDataChange,
  onDocumentUpload,
  onDocumentStatusUpdate,
  applicationId,
  currentStep,
}) => {
  // Extract form data for current stage fields
  const stageFormData: Record<string, any> = {};
  if (stage.customForm && Array.isArray(stage.customForm)) {
    stage.customForm.forEach((field) => {
      const fieldName = field.fieldName;
      if (formData[fieldName] !== undefined) {
        stageFormData[fieldName] = formData[fieldName];
      } else {
      }
    });
  }

  const stageDocuments = uploadedDocuments.filter(
    (doc) => doc.stage_name === stage.stageName
  );

  const handleStageFormChange = useCallback(
    (data: Record<string, any>) => {
      // Merge the stage form data directly into the global form data
      onFormDataChange(data);
    },
    [onFormDataChange]
  );

  const getRequirementStatus = () => {
    const requirements = [];

    if (stage.customFormRequired) {
      const hasFormData = Object.keys(stageFormData).length > 0;
      requirements.push({
        type: "form",
        label: "Custom Form",
        completed: hasFormData,
        icon: FormInput,
      });
    }

    if (stage.documentsRequired) {
      const requiredDocs = stage.documents.filter((doc) => doc.required);
      const uploadedRequiredDocs = stageDocuments.filter((doc) =>
        requiredDocs.some(
          (reqDoc) => normalizeDocument(reqDoc).name === doc.document_name
        )
      );
      const hasAllRequiredDocs =
        requiredDocs.length === uploadedRequiredDocs.length;

      requirements.push({
        type: "documents",
        label: `Documents (${uploadedRequiredDocs.length}/${requiredDocs.length})`,
        completed: hasAllRequiredDocs,
        icon: FileText,
      });
    }

    return requirements;
  };

  const requirements = getRequirementStatus();
  const allRequirementsMet = requirements.every((req) => req.completed);

  // Validate document requirements
  const documentValidationErrors = React.useMemo(() => {
    if (!stage.documentsRequired) return [];

    const requiredDocuments = stage.documents
      .filter((doc) => doc.required)
      .map((doc) => ({
        documentName: normalizeDocument(doc).name,
        required: doc.required,
      }));

    const uploadedDocuments = stageDocuments.map((doc) => ({
      document_name: doc.document_name,
      status: doc.status,
    }));

    return validateDocumentRequirements(requiredDocuments, uploadedDocuments);
  }, [stage.documents, stage.documentsRequired, stageDocuments]);

  return (
    <div className="space-y-6">
      {/* Stage Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <span>
                  Stage {stageIndex + 1}: {stage.stageName}
                </span>
                {allRequirementsMet && (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                )}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Complete the requirements below to proceed to the next stage
              </p>
            </div>
            <div className="flex gap-2">
              {requirements.map((req) => (
                <Badge
                  key={req.type}
                  variant={req.completed ? "default" : "secondary"}
                  className="flex items-center gap-1"
                >
                  <req.icon className="h-3 w-3" />
                  {req.label}
                </Badge>
              ))}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Document Validation Errors */}
      {documentValidationErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">Missing Required Documents:</p>
              <ul className="list-disc list-inside space-y-1">
                {documentValidationErrors.map((error, index) => (
                  <li key={index} className="text-sm">
                    {error.message}
                  </li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Custom Form Section */}
      {stage.customFormRequired && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FormInput className="h-5 w-5" />
              Custom Form
            </CardTitle>
          </CardHeader>
          <CardContent>
            <DynamicForm
              fields={stage.customForm}
              formData={stageFormData}
              onFormDataChange={handleStageFormChange}
              stageIndex={stageIndex}
              applicationId={applicationId}
              currentStep={currentStep}
            />
          </CardContent>
        </Card>
      )}

      {/* Documents Section */}
      {stage.documentsRequired && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Required Documents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <DocumentManager
              documents={stage.documents}
              uploadedDocuments={stageDocuments}
              onDocumentUpload={onDocumentUpload}
              onDocumentStatusUpdate={onDocumentStatusUpdate}
              applicationId={applicationId}
              stageName={stage.stageName}
              stageOrder={stage.stageOrder}
            />
          </CardContent>
        </Card>
      )}

      {/* Stage Summary */}
      {!stage.customFormRequired && !stage.documentsRequired && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Stage Complete</h3>
              <p className="text-muted-foreground">
                This stage doesn&apos;t require any additional information or
                documents.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const StageManager = React.memo(StageManagerComponent);
