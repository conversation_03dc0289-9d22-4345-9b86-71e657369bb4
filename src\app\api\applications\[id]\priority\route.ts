import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { priority_level: priorityLevel } = body;

    // Validate priority level
    const validPriorityLevels = ["Low", "Medium", "High", "Critical"];
    if (!priorityLevel || !validPriorityLevels.includes(priorityLevel)) {
      return NextResponse.json(
        {
          success: false,
          message: `Invalid priority level. Must be one of: ${validPriorityLevels.join(", ")}`,
        },
        { status: 400 }
      );
    }

    // Update application priority in backend
    const backendUrl = `${apiUrl}/applications/${id}/priority`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ priority_level: priorityLevel }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: "Priority level updated successfully",
        data,
      });
    } else {
      console.error("Backend priority update failed:", {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      return NextResponse.json(
        {
          success: false,
          message:
            data.message ||
            `Failed to update priority level (${response.status})`,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating application priority:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error while updating priority",
      },
      { status: 500 }
    );
  }
}
