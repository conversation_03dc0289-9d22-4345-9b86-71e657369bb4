# Git Commit Troubleshooting Guide

## Common Git Commit Issues and Solutions

### 1. **Files Not Staged for Commit**

**Problem**: Running `git commit` without staging files first
**Error Message**: `nothing to commit, working tree clean` or `no changes added to commit`

**Solutions**:

```bash
# Check current status
git status

# Stage all files
git add .

# Stage specific files
git add <filename>
git add src/app/(main)/agents/page.tsx

# Stage files by pattern
git add *.tsx
git add src/**/*.ts
```

### 2. **Git Configuration Issues**

**Problem**: Git user identity not configured
**Error Message**: `Please tell me who you are` or `Author identity unknown`

**Solutions**:

```bash
# Check current configuration
git config --list
git config user.name
git config user.email

# Set global configuration
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Set local repository configuration
git config user.name "Your Name"
git config user.email "<EMAIL>"
```

### 3. **Merge Conflicts**

**Problem**: Unresolved merge conflicts preventing commit
**Error Message**: `error: Committing is not possible because you have unmerged files`

**Solutions**:

```bash
# Check for conflicts
git status
git diff

# Resolve conflicts manually in files, then:
git add <resolved-file>
git commit -m "resolve merge conflicts"

# Abort merge if needed
git merge --abort
```

### 4. **Working Directory Issues**

**Problem**: Uncommitted changes or untracked files
**Error Message**: Various depending on specific issue

**Solutions**:

```bash
# Check working directory status
git status

# See what changes exist
git diff
git diff --staged

# Discard changes if needed
git checkout -- <filename>
git reset HEAD <filename>

# Clean untracked files (be careful!)
git clean -n  # dry run first
git clean -f  # force clean
```

### 5. **Repository State Issues**

**Problem**: Repository in unexpected state (rebase, merge, etc.)
**Error Message**: `fatal: cannot do a partial commit during a merge/rebase`

**Solutions**:

```bash
# Check repository state
git status

# Complete ongoing operations
git rebase --continue
git merge --continue

# Abort ongoing operations if needed
git rebase --abort
git merge --abort
```

### 6. **Permission Issues**

**Problem**: File permission or access issues
**Error Message**: `Permission denied` or `unable to create temporary file`

**Solutions**:

```bash
# Check file permissions
ls -la

# Fix permissions if needed (Linux/Mac)
chmod 644 <filename>
chmod -R 755 .git/

# On Windows, check if files are locked by other processes
```

### 7. **Large Files or Binary Issues**

**Problem**: Files too large or binary files causing issues
**Error Message**: `warning: adding embedded git repository` or size warnings

**Solutions**:

```bash
# Check file sizes
du -sh *

# Use .gitignore for large files
echo "large-file.zip" >> .gitignore

# Remove large files from staging
git reset HEAD large-file.zip
```

## Pre-Commit Checklist

Before attempting any git commit, verify:

1. ✅ **Git Status**: `git status` shows expected changes
2. ✅ **Files Staged**: Changes are staged with `git add`
3. ✅ **Git Config**: User name and email are configured
4. ✅ **No Conflicts**: No unresolved merge conflicts
5. ✅ **Clean State**: Repository not in middle of rebase/merge
6. ✅ **Permissions**: Proper file and directory permissions
7. ✅ **File Sizes**: No unexpectedly large files being committed

## Diagnostic Commands

Run these commands to diagnose git issues:

```bash
# Basic status and configuration
git status
git config --list
git log --oneline -5

# Check for issues
git diff
git diff --staged
git branch -v

# Repository health
git fsck
git gc --prune=now
```

## Emergency Recovery

If git repository becomes corrupted:

```bash
# Backup current work
cp -r . ../backup-$(date +%Y%m%d)

# Reset to last known good state
git reset --hard HEAD
git clean -fd

# If all else fails, re-clone repository
cd ..
git clone <repository-url> new-clone
# Manually copy your changes to new-clone
```
