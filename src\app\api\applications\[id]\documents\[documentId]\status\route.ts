import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string; documentId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id, documentId } = params;

    if (!id || !documentId) {
      return NextResponse.json(
        {
          success: false,
          message: "Application ID and Document ID are required",
        },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { status, reason } = body as DocumentStatusUpdateRequest;

    if (!status) {
      return NextResponse.json(
        { success: false, message: "Status is required" },
        { status: 400 }
      );
    }

    // Validate that reason is provided for specific statuses
    const statusesRequiringReason = [
      "Rejected",
      "Required_Revision",
      "Expired",
      "Under_Review",
    ];
    if (statusesRequiringReason.includes(status) && !reason) {
      return NextResponse.json(
        {
          success: false,
          message: `Reason is required when status is ${status}`,
        },
        { status: 400 }
      );
    }

    // Update document status in backend
    const backendUrl = `${apiUrl}/applications/${id}/documents/${documentId}/status`;

    const response = await fetch(backendUrl, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ status, reason }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: "Document status updated successfully",
        data,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to update document status",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating document status:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
