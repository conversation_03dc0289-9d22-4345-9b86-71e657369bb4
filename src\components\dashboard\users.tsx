import React from "react";
import {
  Card,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { formatDate, isValidUrl } from "@/utils/tools";
import { imgUrl } from "@/utils/urls";

const Users = ({ users }: { users: User[] }) => {
  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle>Latest Users</CardTitle>
        <CardDescription>Recently registered users</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Joined</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-8 w-8 mr-2">
                      <AvatarImage
                        src={
                          isValidUrl(user.image || "")
                            ? `${user.image}`
                            : `${imgUrl}${user.image}`
                        }
                        alt={user.name}
                      />
                      <AvatarFallback className="uppercase">
                        {user.name.slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    <p className="capitalize">{user.name}</p>
                  </div>
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{formatDate(user.createdAt)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default Users;
