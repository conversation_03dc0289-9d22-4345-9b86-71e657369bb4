import React from "react";

import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { PlusIcon } from "lucide-react";
import { NoResults } from "@/loader/no-results";
import { Dialog } from "@/components/ui/dialog";
import { DialogTrigger } from "@radix-ui/react-dialog";
import { getTrainings } from "@/hooks/use-server";
import TrainingForm from "@/components/training/training-form";
import Trainings from "@/components/training/trainings";
const TrainingPage = async () => {
  const data = await getTrainings();
  return (
    <ContentLayout title="Trainings">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Trainings</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Dialog>
          <DialogTrigger className="bg-black p-2 text-white rounded-lg dark:bg-white dark:text-black">
            <PlusIcon />
          </DialogTrigger>
          <TrainingForm training={undefined} />
        </Dialog>
      </div>
      <Content>
        {data.length === 0 && (
          <NoResults
            title="There no Training in database"
            description="Please click on plus icon to add training"
            className="min-h-[calc(100vh-56px-64px-20px-24px-56px-48px)]"
          />
        )}
        <Trainings data={data} />
      </Content>
    </ContentLayout>
  );
};

export default TrainingPage;
