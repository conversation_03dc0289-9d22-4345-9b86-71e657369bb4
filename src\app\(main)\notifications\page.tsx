"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { toast } from "sonner";

import { NotificationSettings } from "@/components/notifications/notification-settings";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";

const NotificationsPage: React.FC = () => {
  const { data: session, status } = useSession();

  const { handle401Error } = useAuthErrorHandler();

  // State management
  const [settings, setSettings] = useState<INotificationSetting[]>([]);
  const [settingsLoading, setSettingsLoading] = useState(false);

  // Fetch notification settings
  const fetchSettings = useCallback(async () => {
    if (!session?.backendTokens?.accessToken) return;

    setSettingsLoading(true);
    try {
      const response = await fetch("/api/notifications/settings", {
        headers: {
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (response.ok) {
        setSettings(result.data || []);
      } else {
        if (response.status === 401) {
          handle401Error();
          return;
        }
        toast.error(result.message || "Failed to fetch notification settings");
        setSettings([]);
      }
    } catch (error) {
      console.error("Error fetching notification settings:", error);
      toast.error("Failed to fetch notification settings");
      setSettings([]);
    } finally {
      setSettingsLoading(false);
    }
  }, [session, handle401Error]);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // Settings event handlers
  const handleSettingsUpdate = () => {
    fetchSettings();
  };

  // Loading state
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">Manage notification settings</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Notification Settings</CardTitle>
        </CardHeader>
        <CardContent>
          {settingsLoading ? (
            <div className="text-center py-8">Loading settings...</div>
          ) : (
            <NotificationSettings
              settings={settings}
              onSettingsUpdate={handleSettingsUpdate}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationsPage;
