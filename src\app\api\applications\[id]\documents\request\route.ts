import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { documentName, reason, documentCategory, stageOrder, required } =
      body as DocumentRequestBody;

    // Validate required fields
    if (
      !documentName ||
      !reason ||
      !documentCategory ||
      stageOrder === undefined ||
      required === undefined
    ) {
      return NextResponse.json(
        {
          success: false,
          message:
            "All fields are required: documentName, reason, documentCategory, stageOrder, required",
        },
        { status: 400 }
      );
    }

    // Validate stageOrder is a positive number
    if (typeof stageOrder !== "number" || stageOrder < 1) {
      return NextResponse.json(
        {
          success: false,
          message: "stageOrder must be a positive number",
        },
        { status: 400 }
      );
    }

    // Validate required is a boolean
    if (typeof required !== "boolean") {
      return NextResponse.json(
        {
          success: false,
          message: "required must be a boolean value",
        },
        { status: 400 }
      );
    }

    // Request new document from backend
    const backendUrl = `${apiUrl}/applications/${id}/documents/request`;

    const response = await fetch(backendUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        documentName,
        reason,
        documentCategory,
        stageOrder,
        required,
      }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: "Document request submitted successfully",
        data,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to submit document request",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error requesting new document:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
