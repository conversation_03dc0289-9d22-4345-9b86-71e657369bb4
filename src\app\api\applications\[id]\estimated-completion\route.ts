import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { estimated_completion: estimatedCompletion } = body;

    if (!estimatedCompletion) {
      return NextResponse.json(
        { success: false, message: "Estimated completion date is required" },
        { status: 400 }
      );
    }

    // Validate date format (ISO string)
    const dateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
    if (!dateRegex.test(estimatedCompletion)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid date format. Expected ISO string.",
        },
        { status: 400 }
      );
    }

    // Update estimated completion date in backend
    const backendUrl = `${apiUrl}/applications/${id}/estimated-completion`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ estimated_completion: estimatedCompletion }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: "Estimated completion date updated successfully",
        data,
      });
    } else {
      console.error("Backend estimated completion update failed:", {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      return NextResponse.json(
        {
          success: false,
          message:
            data.message ||
            `Failed to update estimated completion date (${response.status})`,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating estimated completion date:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "Internal server error while updating estimated completion date",
      },
      { status: 500 }
    );
  }
}
