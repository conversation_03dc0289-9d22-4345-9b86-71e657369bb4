"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Plus, Trash2, X, FileText, FormInput, Loader2 } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useCreateWorkflowTemplate } from "@/hooks/use-query";
import { workflowTemplateSchema } from "@/utils/schema";
import { apiUrl } from "@/utils/urls";
import { useSession } from "next-auth/react";

type FormData = z.infer<typeof workflowTemplateSchema>;

interface CreateWorkflowTemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

// Documents Section Component
const DocumentsSection: React.FC<{
  stageIndex: number;
  form: any;
  documentMasters: IDocumentMaster[];
  loadingMasters: boolean;
  getAvailableDocumentMasters: (
    stageIndex: number,
    docIndex: number
  ) => IDocumentMaster[];
}> = ({
  stageIndex,
  form,
  documentMasters,
  loadingMasters,
  getAvailableDocumentMasters,
}) => {
  const {
    fields: documentFields,
    append: appendDocument,
    remove: removeDocument,
  } = useFieldArray({
    control: form.control,
    name: `workflowTemplate.${stageIndex}.documents`,
  });

  const addDocument = () => {
    appendDocument({ documentName: "", required: false });
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          <span className="font-medium">Required Documents</span>
        </div>
        <Button type="button" onClick={addDocument} size="sm" variant="outline">
          <Plus className="mr-1 h-3 w-3" />
          Add Document
        </Button>
      </div>

      {documentFields.map((document, docIndex) => {
        const availableDocuments = getAvailableDocumentMasters(
          stageIndex,
          docIndex
        );
        return (
          <div
            key={document.id}
            className="flex items-center gap-2 p-3 border rounded-lg"
          >
            <div className="flex-1">
              <FormField
                control={form.control}
                name={`workflowTemplate.${stageIndex}.documents.${docIndex}.documentName`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={loadingMasters}
                      >
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              loadingMasters
                                ? "Loading documents..."
                                : "Select document"
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {availableDocuments.map((doc) => (
                            <SelectItem key={doc.id} value={doc.name}>
                              {doc.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name={`workflowTemplate.${stageIndex}.documents.${docIndex}.required`}
              render={({ field }) => (
                <FormItem className="flex items-center space-x-2">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="text-sm">Required</FormLabel>
                </FormItem>
              )}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => removeDocument(docIndex)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      })}
    </div>
  );
};

// Custom Form Section Component
const CustomFormSection: React.FC<{ stageIndex: number; form: any }> = ({
  stageIndex,
  form,
}) => {
  const {
    fields: formFields,
    append: appendFormField,
    remove: removeFormField,
  } = useFieldArray({
    control: form.control,
    name: `workflowTemplate.${stageIndex}.customForm`,
  });

  const addFormField = () => {
    appendFormField({
      fieldName: "",
      fieldType: "text",
      required: false,
      options: [],
      showToClient: true,
    });
  };

  const fieldTypeOptions = [
    { value: "text", label: "Text" },
    { value: "email", label: "Email" },
    { value: "number", label: "Number" },
    { value: "date", label: "Date" },
    { value: "textarea", label: "Textarea" },
    { value: "select", label: "Select" },
    { value: "checkbox", label: "Checkbox" },
  ];

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FormInput className="h-4 w-4" />
          <span className="font-medium">Custom Form Fields</span>
        </div>
        <Button
          type="button"
          onClick={addFormField}
          size="sm"
          variant="outline"
        >
          <Plus className="mr-1 h-3 w-3" />
          Add Field
        </Button>
      </div>

      {formFields.map((formField, fieldIndex) => {
        const currentFieldType = form.watch(
          `workflowTemplate.${stageIndex}.customForm.${fieldIndex}.fieldType`
        );
        const needsOptions =
          currentFieldType === "select" || currentFieldType === "checkbox";

        return (
          <Card key={formField.id} className="p-4">
            <div className="space-y-4">
              {/* Field Name and Type Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.fieldName`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Field Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter field name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.fieldType`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Field Type *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select field type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {fieldTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Options Field for Select and Checkbox */}
              {needsOptions && (
                <FormField
                  control={form.control}
                  name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.options`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {currentFieldType === "select"
                          ? "Select Options"
                          : "Checkbox Options"}{" "}
                        *
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={`Enter ${currentFieldType === "select" ? "dropdown" : "checkbox"} options, one per line`}
                          value={field.value?.join("\n") || ""}
                          onChange={(e) => {
                            const options = e.target.value
                              .split("\n")
                              .filter((option) => option.trim() !== "");
                            field.onChange(options);
                          }}
                          className="min-h-[80px]"
                        />
                      </FormControl>
                      <div className="text-xs text-muted-foreground">
                        Enter each option on a new line
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Settings Row */}
              <div className="flex items-center justify-between gap-4 pt-2 border-t">
                <div className="flex items-center gap-6">
                  <FormField
                    control={form.control}
                    name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.required`}
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="text-sm font-normal">
                          Required
                        </FormLabel>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.showToClient`}
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="text-sm font-normal">
                          Show to Client
                        </FormLabel>
                      </FormItem>
                    )}
                  />
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFormField(fieldIndex)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export const CreateWorkflowTemplateDialog: React.FC<
  CreateWorkflowTemplateDialogProps
> = ({ open, onOpenChange, onSuccess }) => {
  const [immigrationServices, setImmigrationServices] = useState<
    IImmigration[]
  >([]); // Future: Re-enable when backend endpoint is available
  const [workflowMasters, setWorkflowMasters] = useState<IWorkflowMaster[]>([]);
  const [documentMasters, setDocumentMasters] = useState<IDocumentMaster[]>([]);
  const [loadingMasters, setLoadingMasters] = useState(false);
  const createMutation = useCreateWorkflowTemplate();
  const { data: session } = useSession();

  const form = useForm<FormData>({
    resolver: zodResolver(workflowTemplateSchema),
    defaultValues: {
      name: "",
      description: "",
      serviceId: undefined,
      isActive: true,
      workflowTemplate: [
        {
          stageName: "",
          stageOrder: 1,
          documentsRequired: false,
          documents: [],
          customFormRequired: false,
          customForm: [],
          showToClient: true,
        },
      ],
    },
  });

  const {
    fields: stageFields,
    append: appendStage,
    remove: removeStage,
  } = useFieldArray({
    control: form.control,
    name: "workflowTemplate",
  });

  // Load master data
  useEffect(() => {
    const loadMasterData = async () => {
      if (!session?.backendTokens?.accessToken) return;

      setLoadingMasters(true);
      try {
        // Future: Load immigration services when backend endpoint is available
        const immigrationResponse = await fetch(`${apiUrl}/immigration`, {
          headers: {
            Authorization: `Bearer ${session.backendTokens.accessToken}`,
            "Content-Type": "application/json",
          },
        });

        if (immigrationResponse.ok) {
          const services = await immigrationResponse.json();
          setImmigrationServices(services);
        }

        // Load workflow masters (with high limit to get all active ones)
        const workflowResponse = await fetch(
          `${apiUrl}/workflow-master?page=1&limit=100&is_active=true`,
          {
            headers: {
              Authorization: `Bearer ${session.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (workflowResponse.ok) {
          const workflowData = await workflowResponse.json();
          setWorkflowMasters(workflowData.data || []);
        }

        // Load document masters (with high limit to get all)
        const documentResponse = await fetch(
          `${apiUrl}/document-master?page=1&limit=100`,
          {
            headers: {
              Authorization: `Bearer ${session.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (documentResponse.ok) {
          const documentData = await documentResponse.json();
          setDocumentMasters(documentData.data || []);
        }
      } catch (error) {
        console.error("Failed to load master data:", error);
      } finally {
        setLoadingMasters(false);
      }
    };

    if (open && session) {
      loadMasterData();
    }
  }, [open, session]);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      // Reset form when opening
      form.reset({
        name: "",
        description: "",
        serviceId: undefined,
        isActive: true,
        workflowTemplate: [
          {
            stageName: "",
            stageOrder: 1,
            documentsRequired: false,
            documents: [],
            customFormRequired: false,
            customForm: [],
            showToClient: true,
          },
        ],
      });
    } else {
      // Clear form state when closing to prevent memory leaks
      form.reset();
    }
  }, [open, form]);

  const onSubmit = async (data: FormData) => {
    try {
      // Clean up the data before submission
      const cleanedData = {
        ...data,
        serviceType: "immigration", // Always set to immigration
        serviceId: data.serviceId || undefined, // Ensure empty serviceId is undefined
        workflowTemplate: data.workflowTemplate.map((stage) => ({
          ...stage,
          showToClient:
            stage.showToClient !== undefined ? stage.showToClient : true, // Ensure showToClient is included
          customForm:
            stage.customForm?.map((field) => ({
              ...field,
              showToClient:
                field.showToClient !== undefined ? field.showToClient : true,
            })) || [],
        })),
      };
      await createMutation.mutateAsync(cleanedData);

      // Reset form and close dialog immediately
      form.reset();
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      console.error("Error creating workflow template:", error);
      // Error is handled by the mutation with user-friendly messages
    }
  };

  const addStage = () => {
    const newStageOrder = stageFields.length + 1;
    appendStage({
      stageName: "",
      stageOrder: newStageOrder,
      documentsRequired: false,
      documents: [],
      customFormRequired: false,
      customForm: [],
      showToClient: true, // Default to visible
    });
  };

  // Helper function to get available workflow masters for a specific stage
  const getAvailableWorkflowMasters = (currentStageIndex: number) => {
    const currentStages = form.watch("workflowTemplate") || [];
    const usedStageNames = currentStages
      .map((stage: any, index: number) =>
        index !== currentStageIndex ? stage.stageName : null
      )
      .filter(Boolean);

    return workflowMasters.filter(
      (workflow) => !usedStageNames.includes(workflow.name)
    );
  };

  // Helper function to get available document masters across all stages
  // This prevents documents marked as "required" from being used in multiple stages
  const getAvailableDocumentMasters = useCallback(
    (currentStageIndex: number, currentDocIndex: number) => {
      const currentStages = form.watch("workflowTemplate") || [];
      const requiredDocumentNames: string[] = [];

      currentStages.forEach((stage: any, stageIndex: number) => {
        if (stage.documents && Array.isArray(stage.documents)) {
          stage.documents.forEach((doc: any, docIndex: number) => {
            // Exclude the current document being edited and only filter required documents
            if (
              !(
                stageIndex === currentStageIndex && docIndex === currentDocIndex
              ) &&
              doc.documentName &&
              doc.required === true
            ) {
              requiredDocumentNames.push(doc.documentName);
            }
          });
        }
      });

      // Filter out documents that are already marked as required in other stages
      return documentMasters.filter(
        (doc) => !requiredDocumentNames.includes(doc.name)
      );
    },
    [form, documentMasters]
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Workflow Template</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Basic Information Section */}
            <Card className="p-6">
              <div className="space-y-6">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <div className="h-2 w-2 bg-primary rounded-full"></div>
                  <h3 className="text-lg font-semibold">Basic Information</h3>
                </div>

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter template name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter template description"
                          className="resize-none min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Future: Re-enable Immigration Service dropdown when backend endpoint is available */}
                  <FormField
                    control={form.control}
                    name="serviceId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Immigration Service</FormLabel>
                        <div className="space-y-2">
                          <Select
                            onValueChange={(value) =>
                              field.onChange(
                                value === "none" ? undefined : value
                              )
                            }
                            value={field.value || "none"}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select immigration service" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {immigrationServices.map((service) => (
                                <SelectItem
                                  key={service.id}
                                  value={service.id!}
                                >
                                  {service.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {field.value && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => field.onChange(undefined)}
                              className="text-xs"
                            >
                              Clear Selection
                            </Button>
                          )}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Active Status
                          </FormLabel>
                          <div className="text-sm text-muted-foreground">
                            Enable this template for use
                          </div>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </Card>

            {/* Workflow Stages Section */}
            <div className="space-y-6">
              <Card className="p-6">
                <div className="flex items-center justify-between pb-4 border-b">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-primary rounded-full"></div>
                    <h3 className="text-lg font-semibold">Workflow Stages</h3>
                  </div>
                  <Button
                    type="button"
                    onClick={addStage}
                    size="sm"
                    variant="outline"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Stage
                  </Button>
                </div>
                <div className="mt-6 space-y-6">
                  {stageFields.map((stage, stageIndex) => (
                    <Card key={stage.id} className="relative">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">
                            Stage {stageIndex + 1}
                          </CardTitle>
                          {stageFields.length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeStage(stageIndex)}
                              className="text-destructive hover:text-destructive"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`workflowTemplate.${stageIndex}.stageName`}
                            render={({ field }) => {
                              const availableWorkflows =
                                getAvailableWorkflowMasters(stageIndex);
                              return (
                                <FormItem>
                                  <FormLabel>Stage Name *</FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      value={field.value}
                                      disabled={loadingMasters}
                                    >
                                      <SelectTrigger>
                                        <SelectValue
                                          placeholder={
                                            loadingMasters
                                              ? "Loading workflows..."
                                              : "Select workflow stage"
                                          }
                                        />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {availableWorkflows.map((workflow) => (
                                          <SelectItem
                                            key={workflow.id}
                                            value={workflow.name}
                                          >
                                            {workflow.name}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              );
                            }}
                          />

                          <FormField
                            control={form.control}
                            name={`workflowTemplate.${stageIndex}.stageOrder`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Stage Order *</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="1"
                                    {...field}
                                    onChange={(e) =>
                                      field.onChange(parseInt(e.target.value))
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* Stage Visibility Toggle */}
                        <FormField
                          control={form.control}
                          name={`workflowTemplate.${stageIndex}.showToClient`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel className="text-sm">
                                  Show to Client
                                </FormLabel>
                                <div className="text-xs text-muted-foreground">
                                  Controls whether this stage is visible to
                                  clients
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`workflowTemplate.${stageIndex}.documentsRequired`}
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-sm">
                                    Documents Required
                                  </FormLabel>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`workflowTemplate.${stageIndex}.customFormRequired`}
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-sm">
                                    Custom Form Required
                                  </FormLabel>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* Documents Section */}
                        {form.watch(
                          `workflowTemplate.${stageIndex}.documentsRequired`
                        ) && (
                          <DocumentsSection
                            stageIndex={stageIndex}
                            form={form}
                            documentMasters={documentMasters}
                            loadingMasters={loadingMasters}
                            getAvailableDocumentMasters={
                              getAvailableDocumentMasters
                            }
                          />
                        )}

                        {/* Custom Form Section */}
                        {form.watch(
                          `workflowTemplate.${stageIndex}.customFormRequired`
                        ) && (
                          <CustomFormSection
                            stageIndex={stageIndex}
                            form={form}
                          />
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </Card>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={createMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createMutation.isPending}>
                {createMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {createMutation.isPending
                  ? "Creating Template..."
                  : "Create Template"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
