"use client";
import { useUpdatePaymentProgress } from "@/hooks/use-query";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "../ui/badge";

interface IPaymentProgressProp {
  paymentId: string;
  progress: string;
}

const PaymentProgress: React.FC<IPaymentProgressProp> = ({
  paymentId,
  progress,
}) => {
  const { mutate, isPending } = useUpdatePaymentProgress();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="none"
          className="select-none"
          disabled={isPending}
          aria-label="Open progress menu"
        >
          <Badge
            // @ts-ignore
            variant={
              progressOptions.find((item) => item.name === progress)?.variant
            }
          >
            {progress}
          </Badge>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {progressOptions.map((option) => (
          <DropdownMenuItem
            key={option.name}
            className="w-full text-center cursor-pointer"
            onClick={() =>
              mutate({
                paymentId,
                progress: option.name as
                  | "Accepted"
                  | "Rejected"
                  | "Pending"
                  | "Completed"
                  | "Active"
                  | "Inactive"
                  | "Blocked"
                  | "Cancelled",
              })
            }
          >
            <Badge
              // @ts-ignore
              variant={option.variant}
              className="w-full text-center flex justify-center"
            >
              {option.name}
            </Badge>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PaymentProgress;

const progressOptions = [
  {
    name: "Accepted",
    variant: "Accepted",
  },
  {
    name: "Rejected",
    variant: "Rejected",
  },
  {
    name: "Pending",
    variant: "Pending",
  },
  {
    name: "Completed",
    variant: "Completed",
  },
  {
    name: "Active",
    variant: "Active",
  },
  {
    name: "Inactive",
    variant: "Inactive",
  },
  {
    name: "Blocked",
    variant: "Blocked",
  },
  {
    name: "Cancelled",
    variant: "Cancelled",
  },
];
