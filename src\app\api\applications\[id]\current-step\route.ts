import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { currentStep } = body;

    if (!currentStep) {
      return NextResponse.json(
        { success: false, message: "Current step is required" },
        { status: 400 }
      );
    }

    // Validate currentStep is a valid number
    const nextStepNumber = parseInt(currentStep);
    if (isNaN(nextStepNumber) || nextStepNumber < 1) {
      return NextResponse.json(
        {
          success: false,
          message: "Current step must be a valid positive number",
        },
        { status: 400 }
      );
    }

    // First, fetch the application details to get numberOfSteps for validation
    const applicationUrl = `${apiUrl}/applications/${id}`;
    const applicationResponse = await fetch(applicationUrl, {
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!applicationResponse.ok) {
      return NextResponse.json(
        {
          success: false,
          message: "Failed to fetch application details for validation",
        },
        { status: applicationResponse.status }
      );
    }

    const applicationData = await applicationResponse.json();

    // Validate step progression - check if trying to progress beyond numberOfSteps
    if (
      applicationData.numberOfSteps &&
      nextStepNumber > applicationData.numberOfSteps
    ) {
      return NextResponse.json(
        {
          success: false,
          message: `Cannot progress beyond step ${applicationData.numberOfSteps}. Application is already complete.`,
        },
        { status: 400 }
      );
    }

    // If numberOfSteps is not available, try to calculate from steps array
    if (
      !applicationData.numberOfSteps &&
      applicationData.steps &&
      Array.isArray(applicationData.steps)
    ) {
      const totalSteps = applicationData.steps.length;
      if (totalSteps > 0 && nextStepNumber > totalSteps) {
        return NextResponse.json(
          {
            success: false,
            message: `Cannot progress beyond step ${totalSteps}. Application is already complete.`,
          },
          { status: 400 }
        );
      }
    }

    // Update current step in backend
    const backendUrl = `${apiUrl}/applications/${id}/current-step`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ currentStep }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 401) {
        return NextResponse.json(
          { success: false, message: "Unauthorized" },
          { status: 401 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          message: errorData.message || "Failed to update current step",
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json({
      success: true,
      message: "Current step updated successfully",
      data,
    });
  } catch (error) {
    console.error("Error updating current step:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
