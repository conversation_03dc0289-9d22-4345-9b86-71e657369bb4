"use client";

import React, { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
interface Props {
  children: ReactNode;
}

const NextAuthProvider = ({ children }: Props) => {
  return (
    <SessionProvider
      // Refetch session every hour to keep it fresh
      refetchInterval={60 * 60} // 1 hour in seconds
      // Refetch session when window regains focus
      refetchOnWindowFocus={true}
      // Refetch session when coming back online
      refetchWhenOffline={false}
    >
      {children}
    </SessionProvider>
  );
};

export default NextAuthProvider;
