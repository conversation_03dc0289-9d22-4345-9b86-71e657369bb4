"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useProfile } from "@/hooks/use-query";

export const RoleBasedRedirect: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { data: profileData, isLoading } = useProfile();

  useEffect(() => {
    // Don't redirect if still loading profile data
    if (isLoading || !profileData) return;

    const userRole = profileData?.data?.tokenType;

    // Only redirect agents from the dashboard (/) to applications
    if (userRole === "agent" && pathname === "/") {
      router.replace("/applications");
    }
  }, [profileData, isLoading, pathname, router]);

  // This component doesn't render anything
  return null;
};
