import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { apiUrl } from "@/utils/urls";
import Comments from "@/components/blog/comments";
import { NoResults } from "@/loader/no-results";

export const getComments = async (blogId: string) => {
  const res = await fetch(`${apiUrl}/comment/${blogId}?page=0&limit=0`, {
    next: {
      tags: ["comments", blogId],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IComment[];
  }
  return [];
};

const CommentsPage = async ({
  searchParams,
}: {
  params: { slug: string };
  searchParams?: { [key: string]: string | string[] | undefined };
}) => {
  const data = await getComments(searchParams?.id as string);
  return (
    <ContentLayout title="Blog Comments">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link href="/blog">Blog</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Comments</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <Content>
        {data.length === 0 && (
          <NoResults
            title="No comments yet on this blog"
            description="Awaiting user feedback on this blog"
            className="min-h-[calc(100vh-56px-64px-20px-24px-56px-48px)]"
          />
        )}
        <Comments comments={data} />
      </Content>
    </ContentLayout>
  );
};

export default CommentsPage;
