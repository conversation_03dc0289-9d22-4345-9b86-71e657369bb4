"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useDeleteWorkflowTemplate } from "@/hooks/use-query";

interface DeleteWorkflowTemplateDialogProps {
  template: IWorkflowTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export const DeleteWorkflowTemplateDialog: React.FC<
  DeleteWorkflowTemplateDialogProps
> = ({ template, open, onOpenChange, onSuccess }) => {
  const deleteMutation = useDeleteWorkflowTemplate();

  const handleDelete = async () => {
    if (!template?.id) return;

    try {
      await deleteMutation.mutateAsync(template.id);

      // Close dialog and call success callback with proper cleanup
      onOpenChange(false);
      // Use setTimeout to ensure modal cleanup happens after state updates
      setTimeout(() => {
        onSuccess();
      }, 100);
    } catch (error) {
      // Error is handled by the mutation
      console.error("Error deleting workflow template:", error);
    }
  };

  // Handle dialog close with proper cleanup
  const handleDialogClose = (open: boolean) => {
    onOpenChange(open);
  };

  return (
    <AlertDialog open={open} onOpenChange={handleDialogClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Workflow Template</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the workflow template &quot;
            {template?.name}&quot;? This action cannot be undone and will
            permanently remove the template and all its associated stages and
            configurations.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteMutation.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleteMutation.isPending ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
