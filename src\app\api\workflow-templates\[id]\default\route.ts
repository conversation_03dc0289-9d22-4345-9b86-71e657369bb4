import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * PUT /api/workflow-templates/[id]/default
 *
 * Updates the default status of a workflow template.
 * This endpoint is specifically designed for real-time toggle functionality
 * in the admin interface.
 *
 * @param {NextRequest} request - The incoming request containing isDefault boolean
 * @param {{ params: { id: string } }} params - Route parameters containing the workflow template ID
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Workflow template ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { isDefault } = body;

    if (typeof isDefault !== "boolean") {
      return NextResponse.json(
        { success: false, message: "isDefault must be a boolean value" },
        { status: 400 }
      );
    }

    // Update workflow template default status in backend
    const backendUrl = `${apiUrl}/workflow-templates/${id}/default`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ isDefault }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      return NextResponse.json(
        {
          success: false,
          message:
            data.message || "Failed to update workflow template default status",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating workflow template default status:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
