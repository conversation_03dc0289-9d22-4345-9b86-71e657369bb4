import { format } from "date-fns";

export const isValidUrl = (url: string) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export function formatCurrency(amount: number | string) {
  return new Intl.NumberFormat("en-IE", {
    style: "currency",
    currency: "EUR",
  }).format(Number(amount));
}

export function formatDate(date: string) {
  return format(new Date(date), "MMM dd, yyyy");
}

export const success = {
  style: {
    background: "#e7f6f1",
    color: "#16a87e",
  },
};
export const failed = {
  style: {
    background: "#ffebe6",
    color: "#fd381d",
  },
};
