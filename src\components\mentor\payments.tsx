import React from "react";
import { format } from "date-fns";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Progress from "../common/progress";

const Payments = ({ data }: { data: MentorInfoService[] }) => {
  return (
    <div className="space-y-8">
      {data.map((service) => (
        <div key={service.id} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{service.name}</CardTitle>
              <CardDescription>
                List of users who have purchased this service
              </CardDescription>
            </CardHeader>
            <CardContent>
              {service.users.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Payment Status</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Purchase Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {service.users.map((purchase) => (
                      <TableRow key={purchase.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={purchase?.user?.image || ""}
                                alt={purchase?.user?.name}
                              />
                              <AvatarFallback className="bg-muted">
                                {purchase?.user?.name
                                  .substring(0, 2)
                                  .toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col">
                              <span className="font-medium capitalize">
                                {purchase?.user?.name}
                              </span>
                              <span className="text-sm text-muted-foreground">
                                {purchase?.user?.email}
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>€{purchase?.amount}</TableCell>
                        <TableCell>
                          <Badge variant="success" className="capitalize">
                            {purchase.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Progress
                            id={purchase.id}
                            type="service"
                            status={purchase.progress}
                          />
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          {format(new Date(purchase.createdAt), "PP")}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  No purchases yet for this service
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );
};

export default Payments;
