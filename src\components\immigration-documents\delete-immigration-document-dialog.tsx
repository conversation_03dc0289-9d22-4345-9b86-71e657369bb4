"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useDeleteDocumentMaster } from "@/hooks/use-query";

interface DeleteImmigrationDocumentDialogProps {
  document: IDocumentMaster | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export const DeleteImmigrationDocumentDialog: React.FC<
  DeleteImmigrationDocumentDialogProps
> = ({ document, open, onOpenChange, onSuccess }) => {
  const deleteMutation = useDeleteDocumentMaster();

  const handleDelete = async () => {
    if (!document?.id) return;

    try {
      await deleteMutation.mutateAsync(document.id);
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  if (!document) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Document Master</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the document master &quot;
            {document.name}&quot;? This action cannot be undone and will
            permanently remove the document master from the system.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteMutation.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleteMutation.isPending ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
