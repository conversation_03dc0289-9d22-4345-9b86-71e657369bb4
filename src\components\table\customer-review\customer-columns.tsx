"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import Link from "next/link";
import { Edit, Star } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { isValidUrl } from "@/utils/tools";
import { imgUrl } from "@/utils/urls";
import { Badge } from "@/components/ui/badge";
import DeleteCustomer from "@/components/customer-review/delete-customer";

export const customerColumns: ColumnDef<ICustomerReview>[] = [
  {
    id: "Image",
    cell: ({ row }) => {
      return (
        <Avatar className="w-16 h-16">
          <AvatarImage
            src={
              isValidUrl(row.original.img || "")
                ? `${row.original.img}`
                : `${imgUrl}${row.original.img}`
            }
            alt={row.original.name}
          />
          <AvatarFallback>{row.original.name.charAt(0)}</AvatarFallback>
        </Avatar>
      );
    },
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    id: "source",
    header: () => "Source",
    cell: ({ row }) => (
      <span className="ml-auto inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20">
        {row.original.source}
      </span>
    ),
  },
  {
    id: "rating",
    header: () => "Rating",
    cell: ({ row }) => {
      return (
        <div className="flex gap-1">
          {Array.from({ length: 5 }).map((_, i) => (
            <Star
              key={i}
              className={`w-4 h-4 ${
                i < row.original.rating
                  ? "fill-yellow-400 text-yellow-400"
                  : "text-gray-300"
              }`}
            />
          ))}
        </div>
      );
    },
  },
  {
    id: "order",
    header: () => "Order",
    cell: ({ row }) => (
      <Badge className=" bg-[#404BD0]/10 text-[#404BD0] hover:bg-[#404BD0]/10 hover:text-[#404BD0]">
        {row.original?.order ? row.original?.order : "none"}
      </Badge>
    ),
  },
  {
    id: "Date",
    header: () => "Date",
    cell: ({ row }) => {
      return <p>{format(new Date(row.original.date), "PPP")}</p>;
    },
  },

  {
    id: "Action",
    header: () => "",
    cell: ({ row }) => {
      return (
        <div className="flex  items-center justify-start space-x-3">
          <Link
            href={`/customer-review/${row.original.id}`}
            className="bg-[#404BD0]/10 text-[#404BD0] p-2 rounded-lg"
          >
            <Edit className="w-5 h-5  " />
          </Link>

          <DeleteCustomer id={row.original.id as string} />
        </div>
      );
    },
  },
];
