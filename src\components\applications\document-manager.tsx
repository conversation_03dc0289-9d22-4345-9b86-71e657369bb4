"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Upload,
  FileText,
  Download,
  CheckCircle,
  AlertCircle,
  RefreshCw,
} from "lucide-react";
import { useUploadApplicationDocument } from "@/hooks/use-query";
import { imgUrl } from "@/utils/urls";
import { DocumentStatusManager } from "./document-status-manager";
import { DocumentRequestForm } from "./document-request-form";
import { toast } from "sonner";

interface DocumentManagerProps {
  documents: IWorkflowDocument[] | IApplicationDocumentData[];
  uploadedDocuments: IApplicationDocument[];
  onDocumentUpload: (documents: IApplicationDocument[]) => void;
  onDocumentStatusUpdate?: (
    documentId: string,
    newStatus: DocumentStatus,
    reason?: string
  ) => void;
  applicationId: string;
  stageName: string;
  stageOrder: number;
}

// Helper function to normalize document data
const normalizeDocument = (
  doc: IWorkflowDocument | IApplicationDocumentData
): { name: string; required: boolean; id?: string } => {
  if ("documentName" in doc) {
    // Legacy format
    return { name: doc.documentName, required: doc.required };
  } else {
    // New format
    return { name: doc.fileName, required: doc.required, id: doc.id };
  }
};

export const DocumentManager: React.FC<DocumentManagerProps> = ({
  documents,
  uploadedDocuments,
  onDocumentUpload,
  onDocumentStatusUpdate,
  applicationId,
  stageName,
  stageOrder,
}) => {
  const [uploadingDocs, setUploadingDocs] = useState<Set<string>>(new Set());
  const uploadDocumentMutation = useUploadApplicationDocument(applicationId);

  const handleFileUpload = async (
    file: File,
    documentName: string,
    required: boolean,
    documentId?: string
  ) => {
    setUploadingDocs((prev) => new Set(prev).add(documentName));

    try {
      // Use the new document upload API
      const result = await uploadDocumentMutation.mutateAsync({
        file,
        document_name: documentName,
        stage_order: stageOrder,
        document_id: documentId,
      });

      if (result.success) {
        // Ensure we have a valid document ID from the backend
        if (!result.data?.id) {
          console.error("Backend did not return a document ID:", result);
          throw new Error("Document uploaded but no ID returned from backend");
        }

        // Create a new document object for local state update
        const newDocument: IApplicationDocument = {
          id: result.data.id,
          document_name: documentName,
          file_url: result.data?.file_url || "",
          stage_name: stageName,
          uploaded_at: result.data?.uploaded_at || new Date().toISOString(),
          uploaded_by: result.data?.uploaded_by || "admin",
          status: result.data?.status || "Pending",
          reason: result.data?.reason,
        };

        onDocumentUpload([newDocument]);
      } else {
        throw new Error(result.message || "Upload failed");
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      // Error handling is already done by the mutation hook
    } finally {
      setUploadingDocs((prev) => {
        const newSet = new Set(prev);
        newSet.delete(documentName);
        return newSet;
      });
    }
  };

  const handleFileInputChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    documentName: string,
    required: boolean,
    documentId?: string
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type and size
      const maxSize = 10 * 1024 * 1024; // 10MB
      const allowedTypes = [
        "application/pdf",
        "image/jpeg",
        "image/jpg",
        "image/png",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error("Invalid file type", {
          description: "Please upload PDF, Word document, or image files only.",
        });
        return;
      }

      if (file.size > maxSize) {
        toast.error("File too large", {
          description: "Please upload files smaller than 10MB.",
        });
        return;
      }
      handleFileUpload(file, documentName, required, documentId);
    }
    // Reset input value to allow re-uploading the same file
    event.target.value = "";
  };

  const getDocumentStatus = (documentName: string, required: boolean) => {
    const uploadedDoc = uploadedDocuments.find(
      (doc) => doc.document_name === documentName
    );

    if (uploadedDoc) {
      return {
        status: "uploaded",
        document: uploadedDoc,
        variant: "default" as const,
        icon: CheckCircle,
        label: "Uploaded",
      };
    }

    if (required) {
      return {
        status: "required",
        document: null,
        variant: "destructive" as const,
        icon: AlertCircle,
        label: "Required",
      };
    }

    return {
      status: "optional",
      document: null,
      variant: "secondary" as const,
      icon: FileText,
      label: "Optional",
    };
  };

  const handleDownload = (applicationDocument: IApplicationDocument) => {
    const link = document.createElement("a");
    link.href = imgUrl + applicationDocument.file_url;
    link.download = applicationDocument.document_name;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (documents.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">
          No documents required for this stage.
        </p>
      </div>
    );
  }

  // Preserve the exact order from backend response - no client-side sorting
  const sortedDocuments = documents;

  return (
    <div className="space-y-3">
      {/* Consolidated Documents Card */}
      <Card className="overflow-hidden">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <FileText className="h-5 w-5" />
            Documents ({uploadedDocuments.length}/{documents.length})
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Upload required documents to proceed with your application
          </p>
        </CardHeader>
        <CardContent className="space-y-3">
          {sortedDocuments.map((doc) => {
            const normalizedDoc = normalizeDocument(doc);
            const status = getDocumentStatus(
              normalizedDoc.name,
              normalizedDoc.required
            );
            const isUploading = uploadingDocs.has(normalizedDoc.name);

            return (
              <div
                key={normalizedDoc.name}
                className="group relative p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200 bg-white dark:bg-gray-900 hover:shadow-sm"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div
                      className={`p-1.5 rounded-md transition-transform duration-200 group-hover:scale-105 ${
                        status.status === "uploaded"
                          ? "bg-green-500 text-white"
                          : status.status === "required"
                            ? "bg-red-500 text-white"
                            : "bg-gray-500 text-white"
                      }`}
                    >
                      <status.icon className="h-3 w-3" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">
                        {normalizedDoc.name}
                      </h4>
                      {normalizedDoc.required && (
                        <span className="text-xs text-red-600 dark:text-red-400 font-medium">
                          Required
                        </span>
                      )}
                    </div>
                  </div>
                  <Badge
                    variant={status.variant}
                    className="flex items-center gap-1 text-xs shrink-0"
                  >
                    {status.label}
                  </Badge>
                </div>
                {status.document ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-950/20 rounded-md border border-green-200 dark:border-green-800">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <div className="p-1.5 bg-green-500 text-white rounded-md">
                          <CheckCircle className="h-3 w-3" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-sm text-green-900 dark:text-green-100 truncate">
                            {status.document.document_name}
                          </p>
                          <p className="text-xs text-green-700 dark:text-green-300">
                            {new Date(
                              status.document.uploaded_at
                            ).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-1 shrink-0">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownload(status.document!)}
                          className="h-7 w-7 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
                          title="Download"
                        >
                          <Download className="h-3 w-3" />
                        </Button>
                        {/* Re-upload button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const fileInput = document.getElementById(
                              `reupload-${normalizedDoc.name}`
                            ) as HTMLInputElement;
                            fileInput?.click();
                          }}
                          className="h-7 w-7 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20 text-blue-600 hover:text-blue-700"
                          title="Re-upload"
                          disabled={isUploading}
                        >
                          <Upload className="h-3 w-3" />
                        </Button>
                      </div>
                      {/* Hidden file input for re-upload */}
                      <input
                        id={`reupload-${normalizedDoc.name}`}
                        type="file"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        style={{ display: "none" }}
                        onChange={(e) =>
                          handleFileInputChange(
                            e,
                            normalizedDoc.name,
                            normalizedDoc.required,
                            normalizedDoc.id
                          )
                        }
                      />
                    </div>
                    {/* Document Status Manager */}
                    <div className="flex justify-end">
                      <DocumentStatusManager
                        document={status.document}
                        applicationId={applicationId}
                        onStatusUpdate={(
                          newStatus: DocumentStatus,
                          reason?: string
                        ) => {
                          // Update the document status in parent component
                          onDocumentStatusUpdate?.(
                            status.document.id,
                            newStatus,
                            reason
                          );
                        }}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Upload className="h-4 w-4 text-gray-500" />
                      <Label
                        htmlFor={`file-${normalizedDoc.name}`}
                        className="text-sm font-medium"
                      >
                        Upload {normalizedDoc.name}
                      </Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Input
                        id={`file-${normalizedDoc.name}`}
                        type="file"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        onChange={(e) =>
                          handleFileInputChange(
                            e,
                            normalizedDoc.name,
                            normalizedDoc.required,
                            normalizedDoc.id
                          )
                        }
                        disabled={isUploading}
                        className="flex-1 text-sm"
                      />
                      <Button
                        variant={normalizedDoc.required ? "default" : "outline"}
                        size="sm"
                        disabled={isUploading}
                        className="px-3 py-2 h-9 text-xs font-medium"
                      >
                        {isUploading ? (
                          <>
                            <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                            Uploading
                          </>
                        ) : (
                          <>
                            <Upload className="h-3 w-3 mr-1" />
                            Upload
                          </>
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Supported: PDF, DOC, DOCX, JPG, JPEG, PNG (Max 20MB)
                    </p>
                  </div>
                )}
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* Document Request Form */}
      <Card className="border-dashed border-2 border-gray-300 dark:border-gray-600 bg-gray-50/50 dark:bg-gray-800/50">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500 text-white rounded-lg">
                <FileText className="h-4 w-4" />
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  Need Additional Documents?
                </p>
                <p className="text-sm text-muted-foreground">
                  Request new documents from the applicant
                </p>
              </div>
            </div>
            <DocumentRequestForm
              applicationId={applicationId}
              currentStageOrder={stageOrder}
              onDocumentRequested={() => {
                // Refresh the document list or trigger a re-fetch
                // This could be enhanced to update local state
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500 text-white rounded-lg">
                <CheckCircle className="h-5 w-5" />
              </div>
              <div>
                <p className="font-medium text-blue-900 dark:text-blue-100">
                  Document Status
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {uploadedDocuments.length} of {documents.length} documents
                  uploaded
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                Required Documents
              </p>
              <p className="font-bold text-lg text-blue-900 dark:text-blue-100">
                {
                  uploadedDocuments.filter((doc) =>
                    documents.find(
                      (d) =>
                        normalizeDocument(d).name === doc.document_name &&
                        normalizeDocument(d).required
                    )
                  ).length
                }{" "}
                of{" "}
                {documents.filter((d) => normalizeDocument(d).required).length}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
