"use client";

import React, { ReactNode } from "react";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";

interface AuthGuardProps {
  children: ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isLoading } = useAuthErrorHandler();

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, the auth error handler will redirect to login
  // We still render children to avoid flash of content
  return <>{children}</>;
};
