"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useAssignAgent } from "@/hooks/use-query";
import { apiUrl } from "@/utils/urls";

interface AssignAgentDialogProps {
  application: IApplication;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export const AssignAgentDialog: React.FC<AssignAgentDialogProps> = ({
  application,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const { data: session } = useSession();
  const assignAgentMutation = useAssignAgent();

  const [agents, setAgents] = useState<IAgent[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const fetchAgents = useCallback(async () => {
    if (!session) return;

    setLoading(true);
    try {
      const response = await fetch(`${apiUrl}/agents?limit=100&status=Active`, {
        headers: {
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const result = await response.json();
        setAgents(result.data || []);
      }
    } catch (error) {
      console.error("Error fetching agents:", error);
    } finally {
      setLoading(false);
    }
  }, [session]);

  // Fetch agents when dialog opens
  useEffect(() => {
    if (open && session) {
      fetchAgents();
    }
  }, [open, session, fetchAgents]);

  // Set current assigned agent when dialog opens
  useEffect(() => {
    // Check for new API format first (agent_ids), then fallback to legacy format (assigned_agent)
    let agents: IAgent[] = [];

    if (application.agent_ids && Array.isArray(application.agent_ids)) {
      // New API format - agent_ids is already an array of agent objects
      agents = application.agent_ids;
    } else if (application.assigned_agent) {
      // Legacy format - handle both single agent and array of agents
      agents = Array.isArray(application.assigned_agent)
        ? application.assigned_agent
        : [application.assigned_agent];
    }

    if (agents.length > 0 && agents[0].id) {
      setSelectedAgentId(agents[0].id); // For now, show the first agent if multiple
    } else {
      setSelectedAgentId("unassigned");
    }
  }, [application.assigned_agent, application.agent_ids]);

  const handleAssign = async () => {
    if (!selectedAgentId) return;

    try {
      // Handle unassign case with special value
      const agentIdToAssign =
        selectedAgentId === "unassigned" ? "" : selectedAgentId;

      await assignAgentMutation.mutateAsync({
        applicationId: application.id,
        agentId: agentIdToAssign,
      });
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the mutation
      console.error("Failed to assign agent:", error);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      // Reset to current assigned agent when closing
      if (application.assigned_agent) {
        const agents = Array.isArray(application.assigned_agent)
          ? application.assigned_agent
          : [application.assigned_agent];
        setSelectedAgentId(
          agents.length > 0 && agents[0].id ? agents[0].id : "unassigned"
        );
      } else {
        setSelectedAgentId("unassigned");
      }
    }
    onOpenChange(newOpen);
  };

  // Get current agent for display (first agent if multiple)
  const currentAgent = application.assigned_agent
    ? Array.isArray(application.assigned_agent)
      ? application.assigned_agent[0]
      : application.assigned_agent
    : null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Assign Agent</DialogTitle>
          <DialogDescription>
            Assign an agent to application {application.application_number}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {currentAgent && (
            <div className="p-3 bg-muted rounded-md">
              <Label className="text-sm font-medium">Currently Assigned:</Label>
              <p className="text-sm text-muted-foreground">
                {currentAgent.name} ({currentAgent.email})
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="agent-select">Select Agent</Label>
            <Select
              value={selectedAgentId}
              onValueChange={setSelectedAgentId}
              disabled={loading || assignAgentMutation.isPending}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={
                    loading ? "Loading agents..." : "Select an agent"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unassigned">No Agent (Unassign)</SelectItem>
                {agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id!}>
                    {agent.name} ({agent.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={assignAgentMutation.isPending}
          >
            Cancel
          </Button>

          <Button
            onClick={handleAssign}
            disabled={
              !selectedAgentId ||
              assignAgentMutation.isPending ||
              selectedAgentId === currentAgent?.id ||
              (selectedAgentId === "unassigned" && !currentAgent)
            }
          >
            {assignAgentMutation.isPending
              ? selectedAgentId === "unassigned"
                ? "Unassigning..."
                : "Assigning..."
              : selectedAgentId === "unassigned"
                ? "Unassign Agent"
                : "Assign Agent"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
