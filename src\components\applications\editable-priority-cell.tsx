"use client";

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Check, X, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface EditablePriorityCellProps {
  applicationId: string;
  currentPriority: string;
  onPriorityUpdate: (
    applicationId: string,
    newPriority: string
  ) => Promise<void>;
}

const priorityOptions = [
  { value: "Low", label: "Low" },
  { value: "Medium", label: "Medium" },
  { value: "High", label: "High" },
  { value: "Critical", label: "Critical" },
];

const getPriorityBadgeVariant = (priority: string) => {
  switch (priority.toLowerCase()) {
    case "high":
      return "destructive";
    case "medium":
      return "secondary";
    case "low":
      return "outline";
    case "critical":
      return "destructive";
    default:
      return "outline";
  }
};

export const EditablePriorityCell: React.FC<EditablePriorityCellProps> = ({
  applicationId,
  currentPriority,
  onPriorityUpdate,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState(currentPriority);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
    setSelectedPriority(currentPriority);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setSelectedPriority(currentPriority);
  };

  const handleSave = async () => {
    if (selectedPriority === currentPriority) {
      setIsEditing(false);
      return;
    }

    setIsUpdating(true);
    try {
      await onPriorityUpdate(applicationId, selectedPriority);
      setIsEditing(false);
      toast.success(
        `Priority level updated to ${selectedPriority} successfully`
      );
    } catch (error) {
      console.error("Failed to update priority:", error);

      // Provide user-friendly error message
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to update priority level";

      toast.error(errorMessage);
      setSelectedPriority(currentPriority); // Reset to original value
    } finally {
      setIsUpdating(false);
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <Select value={selectedPriority} onValueChange={setSelectedPriority}>
          <SelectTrigger className="w-[120px] h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {priorityOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="flex gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleSave}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Check className="h-3 w-3" />
            )}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleCancel}
            disabled={isUpdating}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Badge
      variant={getPriorityBadgeVariant(currentPriority)}
      className="capitalize cursor-pointer hover:opacity-80"
      onClick={handleEdit}
    >
      {currentPriority}
    </Badge>
  );
};
