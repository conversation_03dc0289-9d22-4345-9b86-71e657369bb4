import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { getCustomerReview } from "@/hooks/use-server";
import CustomerReviewForm from "@/components/customer-review/customer-review-form";

const EditCustomerReviewPage = async ({
  params,
}: {
  params: { id: string };
}) => {
  const data = await getCustomerReview(params.id);

  return (
    <ContentLayout title="Edit Customer Review">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link href="/customer-review">Customer Review</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{data?.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <Content>{data && <CustomerReviewForm customer={data} />}</Content>
    </ContentLayout>
  );
};

export default EditCustomerReviewPage;
