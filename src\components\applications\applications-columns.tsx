"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import Link from "next/link";
import { Eye, UserPlus, ExternalLink } from "lucide-react";
import { EditablePriorityCell } from "./editable-priority-cell";
import { WorkflowTemplateSelector } from "./workflow-template-selector";

export const createApplicationsColumns = (
  onPriorityUpdate: (
    applicationId: string,
    newPriority: string
  ) => Promise<void>,
  onAssignAgent: (application: IApplication) => void,
  userRole?: "user" | "admin" | "agent",
  onWorkflowTemplateUpdate?: () => void
): ColumnDef<IApplication>[] => [
  {
    id: "application_number",
    header: "Application #",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="font-medium w-[150px]">
          <Link
            href={`/applications/${application.id}`}
            className="text-primary hover:underline flex items-center gap-1"
          >
            {application.application_number}
            <ExternalLink className="h-3 w-3" />
          </Link>
        </div>
      );
    },
  },
  {
    id: "service_name",
    header: "Service Name",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="font-medium">{application.service_name || "N/A"}</div>
      );
    },
  },
  {
    id: "status",
    header: "Status",
    cell: ({ row }) => {
      const application = row.original;

      // Calculate status based on current_step and numberOfSteps
      let computedStatus = "Pending";
      let statusVariant: "default" | "secondary" | "outline" | "destructive" =
        "outline";

      // If numberOfSteps is available, use it for computation
      if (application.numberOfSteps && application.numberOfSteps > 0) {
        if (application.current_step >= application.numberOfSteps) {
          computedStatus = "Completed";
          statusVariant = "default";
        } else {
          computedStatus = "Pending";
          statusVariant = "secondary";
        }
      } else if (application.steps && Array.isArray(application.steps)) {
        // Fallback: use steps array length
        const totalSteps = application.steps.length;
        if (totalSteps > 0 && application.current_step >= totalSteps) {
          computedStatus = "Completed";
          statusVariant = "default";
        } else {
          computedStatus = "Pending";
          statusVariant = "secondary";
        }
      } else {
        // Fallback to original status if no step information is available
        computedStatus = application.status;
        switch (application.status.toLowerCase()) {
          case "completed":
            statusVariant = "default";
            break;
          case "in_progress":
            statusVariant = "secondary";
            break;
          case "pending":
            statusVariant = "outline";
            break;
          case "rejected":
            statusVariant = "destructive";
            break;
          default:
            statusVariant = "outline";
        }
      }

      return (
        <Badge variant={statusVariant} className="capitalize">
          {computedStatus}
        </Badge>
      );
    },
  },
  {
    id: "priority",
    header: "Priority",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <EditablePriorityCell
          applicationId={application.id}
          currentPriority={application.priority_level}
          onPriorityUpdate={onPriorityUpdate}
        />
      );
    },
  },

  {
    id: "assigned_agent",
    header: "Assigned Agent",
    cell: ({ row }) => {
      const application = row.original;

      // Check for new API format first (agent_ids), then fallback to legacy format (assigned_agent)
      let agents: IAgent[] = [];

      if (application.agent_ids && Array.isArray(application.agent_ids)) {
        // New API format - agent_ids is already an array of agent objects
        agents = application.agent_ids;
      } else if (application.assigned_agent) {
        // Legacy format - handle both single agent and array of agents
        agents = Array.isArray(application.assigned_agent)
          ? application.assigned_agent
          : [application.assigned_agent];
      }

      // Handle empty agent arrays or no agents assigned
      if (!agents || agents.length === 0) {
        return (
          <span className="text-muted-foreground text-sm">Unassigned</span>
        );
      }

      // Single agent display
      if (agents.length === 1) {
        const agent = agents[0];
        return (
          <div className="flex flex-col">
            <span className="font-medium text-sm">{agent.name}</span>
            <span className="text-xs text-muted-foreground">{agent.email}</span>
          </div>
        );
      }

      // Multiple agents - show count and first agent
      const firstAgent = agents[0];
      return (
        <div className="flex flex-col">
          <span className="font-medium text-sm">
            {firstAgent.name} +{agents.length - 1} more
          </span>
          <span className="text-xs text-muted-foreground">
            {agents.length} agents assigned
          </span>
        </div>
      );
    },
  },
  {
    id: "user_details",
    header: "User Details",
    cell: ({ row }) => {
      const application = row.original;

      // Extract user information from different possible sources
      const name = (application as any).guest_name ||
        application.guest?.name ||
        (application as any).user?.name ||
        "N/A";

      const email = (application as any).guest_email ||
        application.guest?.email ||
        (application as any).user?.email ||
        "N/A";

      const mobile = (application as any).guest_mobile ||
        application.guest?.mobile ||
        (application as any).user?.mobile ||
        "N/A";

      return (
        <div className="space-y-1">
          <div className="font-medium text-sm">{name}</div>
          <div className="text-xs text-muted-foreground">{email}</div>
          <div className="text-xs text-muted-foreground">{mobile}</div>
        </div>
      );
    },
  },
  {
    id: "workflow_template",
    header: "Workflow Template",
    cell: ({ row }) => {
      const application = row.original;

      // Only show workflow template selector for admin users
      if (userRole === "admin") {
        return (
          <WorkflowTemplateSelector
            applicationId={application.id}
            serviceId={application.service_id}
            currentWorkflowTemplateId={application.workflow_template?.id}
            currentWorkflowTemplateName={application.workflow_template?.name}
            onUpdate={onWorkflowTemplateUpdate}
          />
        );
      }

      // For non-admin users, just show the template name
      return (
        <div className="text-sm">
          {application.workflow_template?.name || "N/A"}
        </div>
      );
    },
  },
  {
    id: "created_at",
    header: "Created",
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.created_at), "MMM dd, yyyy")}
      </div>
    ),
  },
  {
    id: "updated_at",
    header: "Updated",
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.updated_at), "MMM dd, yyyy")}
      </div>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="flex items-center gap-1 w-[120px]">
          {/* Show Assign Agent button only for admin users */}
          {userRole === "admin" && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAssignAgent(application)}
              className="h-8 w-8 p-0"
              title="Assign Agent"
            >
              <UserPlus className="h-4 w-4" />
              <span className="sr-only">Assign agent</span>
            </Button>
          )}
          {/* Show View button for all users */}
          <Button variant="ghost" size="sm" asChild className="h-8 w-8 p-0">
            <Link href={`/applications/${application.id}`}>
              <Eye className="h-4 w-4" />
              <span className="sr-only">View application details</span>
            </Link>
          </Button>
        </div>
      );
    },
  },
];
