import React from "react";
import { Card, CardContent } from "../ui/card";

interface ContentLayoutProps {
  children: React.ReactNode;
}

export function Content({ children }: ContentLayoutProps) {
  return (
    <Card className="rounded-lg border-none mt-6">
      <CardContent className="p-6 space-y-8 min-h-[calc(100vh-56px-64px-20px-24px-56px-48px)]">
        {children}
      </CardContent>
    </Card>
  );
}
