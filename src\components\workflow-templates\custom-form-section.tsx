"use client";

import React from "react";
import { useFieldArray } from "react-hook-form";
import { Plus, X } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

interface CustomFormSectionProps {
  stageIndex: number;
  form: any;
}

export const CustomFormSection: React.FC<CustomFormSectionProps> = ({
  stageIndex,
  form,
}) => {
  const {
    fields: formFields,
    append: appendFormField,
    remove: removeFormField,
  } = useFieldArray({
    control: form.control,
    name: `workflowTemplate.${stageIndex}.customForm`,
  });

  const addFormField = () => {
    appendFormField({
      fieldName: "",
      fieldType: "text",
      required: false,
      options: [],
      showToClient: true,
    });
  };

  const fieldTypeOptions = [
    { value: "text", label: "Text" },
    { value: "email", label: "Email" },
    { value: "number", label: "Number" },
    { value: "date", label: "Date" },
    { value: "textarea", label: "Textarea" },
    { value: "select", label: "Select" },
    { value: "checkbox", label: "Checkbox" },
  ];

  return (
    <Card className="mt-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Custom Form Fields</CardTitle>
          <Button
            type="button"
            onClick={addFormField}
            size="sm"
            variant="outline"
          >
            <Plus className="mr-2 h-3 w-3" />
            Add Field
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {formFields.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-4">
            No form fields added yet. Click &quot;Add Field&quot; to get
            started.
          </p>
        ) : (
          formFields.map((field, fieldIndex) => (
            <div key={field.id} className="p-4 border rounded-lg space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Field {fieldIndex + 1}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFormField(fieldIndex)}
                  className="text-destructive hover:text-destructive"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.fieldName`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Field Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter field name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.fieldType`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Field Type *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select field type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {fieldTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Options field for select and checkbox types */}
              {(form.watch(
                `workflowTemplate.${stageIndex}.customForm.${fieldIndex}.fieldType`
              ) === "select" ||
                form.watch(
                  `workflowTemplate.${stageIndex}.customForm.${fieldIndex}.fieldType`
                ) === "checkbox") && (
                <FormField
                  control={form.control}
                  name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.options`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Options (comma-separated)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Male, Female, Other"
                          value={field.value?.join(", ") || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            const options = value
                              .split(",")
                              .map((opt) => opt.trim())
                              .filter(Boolean);
                            field.onChange(options);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.required`}
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm">
                          Required Field
                        </FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`workflowTemplate.${stageIndex}.customForm.${fieldIndex}.showToClient`}
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm">
                          Show to Client
                        </FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
};
