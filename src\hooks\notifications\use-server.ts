import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";
// Get Notification Settings
export const getNotificationSettings = async (userType?: string) => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const params = new URLSearchParams();
  if (userType) params.append("userType", userType);

  const res = await fetch(
    `${apiUrl}/notifications/settings?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["notification-settings"],
      },
      cache: "no-store",
    }
  );

  const data = await res.json();

  if (res.status === 200) {
    return data as { data: INotificationSetting[] };
  }

  return { data: [] };
};
