"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useUpdateNotificationSettings } from "@/hooks/notifications/use-query";

interface NotificationSettingsProps {
  settings: INotificationSetting[];
  onSettingsUpdate: () => void;
}

// Notification types with descriptions
const NOTIFICATION_TYPES = [
  {
    type: "document_reminder",
    label: "Document Reminder",
    description: "Reminders for pending document uploads",
    hasIntervals: true,
  },
  {
    type: "application_submitted",
    label: "Application Submitted",
    description: "Confirmation when application is submitted",
    hasIntervals: false,
  },
  {
    type: "agent_assigned",
    label: "Agent Assigned",
    description: "Notification when agent is assigned to application",
    hasIntervals: false,
  },
  {
    type: "status_update",
    label: "Status Update",
    description: "Updates on application status changes",
    hasIntervals: false,
  },
  {
    type: "agent_query",
    label: "Agent Query",
    description: "Notifications for agent queries and responses",
    hasIntervals: false,
  },
  {
    type: "document_rejected",
    label: "Document Rejected",
    description: "Alerts when documents are rejected",
    hasIntervals: false,
  },
  {
    type: "authority_query",
    label: "Authority Query",
    description: "Notifications for authority queries",
    hasIntervals: false,
  },
  {
    type: "deadline_warning",
    label: "Deadline Warning",
    description: "Warnings for approaching deadlines",
    hasIntervals: true,
  },
  {
    type: "missing_document",
    label: "Missing Document",
    description: "Alerts for missing required documents",
    hasIntervals: true,
  },
  {
    type: "eligibility_confirmation",
    label: "Eligibility Confirmation",
    description: "Service eligibility confirmations",
    hasIntervals: false,
  },
  {
    type: "payment_confirmation",
    label: "Payment Confirmation",
    description: "Payment processing confirmations",
    hasIntervals: false,
  },
  {
    type: "final_decision",
    label: "Final Decision",
    description: "Final application decision notifications",
    hasIntervals: false,
  },
  {
    type: "system_maintenance",
    label: "System Maintenance",
    description: "System maintenance and downtime alerts",
    hasIntervals: false,
  },
  {
    type: "escalation_notice",
    label: "Escalation Notice",
    description: "Internal escalation and reassignment notifications",
    hasIntervals: false,
  },
];

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  settings,
  onSettingsUpdate,
}) => {
  const [localSettings, setLocalSettings] = useState<
    Record<string, INotificationSetting>
  >({});
  const [hasChanges, setHasChanges] = useState(false);
  const updateMutation = useUpdateNotificationSettings();

  // Initialize local settings from props
  useEffect(() => {
    const settingsMap: Record<string, INotificationSetting> = {};

    // Create default settings for all notification types
    NOTIFICATION_TYPES.forEach((notifType) => {
      const existingSetting = settings.find(
        (s) => s.notificationType === notifType.type
      );
      settingsMap[notifType.type] = existingSetting || {
        userType: "admin",
        notificationType: notifType.type as NotificationType,
        channels: ["email"], // Default to email only
        isEnabled: true,
        customSchedule: notifType.hasIntervals ? 24 : undefined, // Default to 24 hours for interval types
      };
    });

    setLocalSettings(settingsMap);
  }, [settings]);

  const handleSettingChange = (
    notificationType: string,
    field: keyof INotificationSetting,
    value: any
  ) => {
    setLocalSettings((prev) => ({
      ...prev,
      [notificationType]: {
        ...prev[notificationType],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      const settingsArray = Object.values(localSettings);
      await updateMutation.mutateAsync(settingsArray);
      setHasChanges(false);
      onSettingsUpdate();
    } catch (error) {
      console.error("Failed to update notification settings:", error);
    }
  };

  const handleReset = () => {
    const settingsMap: Record<string, INotificationSetting> = {};
    settings.forEach((setting) => {
      settingsMap[setting.notificationType] = { ...setting };
    });
    setLocalSettings(settingsMap);
    setHasChanges(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Notification Preferences</h3>
          <p className="text-sm text-muted-foreground">
            Configure how and when you receive notifications
          </p>
        </div>
        <div className="flex gap-2">
          {hasChanges && (
            <Button variant="outline" onClick={handleReset}>
              Reset
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={!hasChanges || updateMutation.isPending}
          >
            {updateMutation.isPending ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {NOTIFICATION_TYPES.map((notifType) => {
          const setting = localSettings[notifType.type];
          if (!setting) return null;

          return (
            <Card key={notifType.type}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">
                      {notifType.label}
                    </CardTitle>
                    <CardDescription>{notifType.description}</CardDescription>
                  </div>
                  <Switch
                    checked={setting.isEnabled}
                    onCheckedChange={(checked) =>
                      handleSettingChange(notifType.type, "isEnabled", checked)
                    }
                  />
                </div>
              </CardHeader>

              {setting.isEnabled && (
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* Custom Schedule for applicable types */}
                    {notifType.hasIntervals && (
                      <div>
                        <Label className="text-sm font-medium">
                          Custom Schedule (hours)
                        </Label>
                        <Input
                          type="number"
                          min="1"
                          max="8760"
                          value={setting.customSchedule || 24}
                          onChange={(e) =>
                            handleSettingChange(
                              notifType.type,
                              "customSchedule",
                              parseInt(e.target.value) || 24
                            )
                          }
                          className="w-32 mt-2"
                          placeholder="24"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Reminder will be sent after this many hours from the
                          initial trigger
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>
    </div>
  );
};
