"use client";

import React, { useState, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { WorkflowStepper } from "./workflow-stepper";
import { StageManager } from "./stage-manager";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ProgressStageSelect } from "@/components/applications/progress-stage-button";

interface ApplicationDetailProps {
  application: IApplicationDetail | IApplicationData;
}

// Helper function to transform application data to expected format
const transformApplicationData = (
  app: IApplicationDetail | IApplicationData
): {
  stages: IWorkflowStage[];
  currentStep: number;
  formData: Record<string, any>;
  uploadedDocuments: IApplicationDocument[];
} => {
  // Check if this is the new JSON format
  if (
    "steps" in app &&
    Array.isArray(app.steps) &&
    app.steps.length > 0 &&
    "stageOrder" in app.steps[0]
  ) {
    const applicationData = app as IApplicationData;

    // Transform steps to workflow stages
    const stages: IWorkflowStage[] = applicationData.steps.map(
      (step: IApplicationStep) => ({
        stageName: step.stageName,
        stageOrder: step.stageOrder,
        documentsRequired: step.documentsRequired,
        customFormRequired: step.customFormRequired,
        documents: step.documents, // Pass the full IApplicationDocumentData objects
        customForm: step.customForm, // Keep original IApplicationFormField with fieldValue
      })
    );

    // Transform documents to expected format
    const uploadedDocuments: IApplicationDocument[] =
      applicationData.steps.flatMap((step: IApplicationStep) =>
        step.documents
          .filter((doc: IApplicationDocumentData) => doc.fileUrl) // Only include uploaded documents
          .map((doc: IApplicationDocumentData) => ({
            id: doc.id,
            document_name: doc.fileName,
            file_url: doc.fileUrl,
            stage_name: step.stageName,
            uploaded_at: doc.uploadDate,
            uploaded_by: "user", // Default value
            status: doc.status as DocumentStatus, // Include document status
            reason: doc.requestReason, // Include reason if available
          }))
      );

    // Extract form data from fieldValue properties
    const formData: Record<string, any> = {};
    applicationData.steps.forEach((step: IApplicationStep) => {
      step.customForm.forEach((field: IApplicationFormField) => {
        if (field.fieldValue !== undefined) {
          formData[field.fieldName] = field.fieldValue;
        }
      });
    });

    return {
      stages,
      currentStep: parseInt(applicationData.current_step) - 1,
      formData,
      uploadedDocuments,
    };
  }

  // Handle legacy format
  const legacyApp = app as IApplicationDetail;
  const workflowTemplate = legacyApp.workflow_template;

  if (
    !workflowTemplate ||
    !workflowTemplate.workflowTemplate ||
    !Array.isArray(workflowTemplate.workflowTemplate)
  ) {
    return {
      stages: [],
      currentStep: 0,
      formData: {},
      uploadedDocuments: [],
    };
  }

  return {
    stages: workflowTemplate.workflowTemplate,
    currentStep: legacyApp.current_step - 1,
    formData: legacyApp.form_data || {},
    uploadedDocuments: legacyApp.uploaded_documents || [],
  };
};

export const ApplicationDetail: React.FC<ApplicationDetailProps> = ({
  application,
}) => {
  const transformedData = transformApplicationData(application);
  const [currentStage, setCurrentStage] = useState(transformedData.currentStep);
  const [formData, setFormData] = useState(transformedData.formData);
  const [uploadedDocuments, setUploadedDocuments] = useState(
    transformedData.uploadedDocuments
  );

  // Calculate numberOfSteps from application data
  const numberOfSteps = (() => {
    // First try to get numberOfSteps from application data (type-safe access)
    if (
      "numberOfSteps" in application &&
      application.numberOfSteps &&
      application.numberOfSteps > 0
    ) {
      return application.numberOfSteps;
    }

    // Fallback: calculate from stages/steps array
    if (transformedData.stages && transformedData.stages.length > 0) {
      return transformedData.stages.length;
    }

    // If application has steps array, use its length
    if (
      application.steps &&
      Array.isArray(application.steps) &&
      application.steps.length > 0
    ) {
      return application.steps.length;
    }

    return undefined;
  })();

  // Move all hooks to the top before any conditional returns
  const handleStageChange = (stageIndex: number) => {
    setCurrentStage(stageIndex);
  };

  const handleFormDataChange = useCallback((stageData: Record<string, any>) => {
    setFormData((prev) => {
      const newData = { ...prev, ...stageData };
      // Only update if the data has actually changed
      if (JSON.stringify(prev) !== JSON.stringify(newData)) {
        return newData;
      }
      return prev;
    });
  }, []);

  const handleDocumentUpload = useCallback(
    (documents: IApplicationDocument[]) => {
      setUploadedDocuments((prev) => [...prev, ...documents]);
    },
    []
  );

  const handleDocumentStatusUpdate = useCallback(
    (documentId: string, newStatus: DocumentStatus, reason?: string) => {
      // Update the document status in local state immediately
      setUploadedDocuments((prev) =>
        prev.map((doc) =>
          doc.id === documentId
            ? { ...doc, status: newStatus, reason: reason }
            : doc
        )
      );
    },
    []
  );

  const stages = transformedData.stages;

  if (stages.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <h3 className="text-lg font-semibold text-muted-foreground mb-2">
              No Workflow Template
            </h3>
            <p className="text-muted-foreground mb-4">
              This application doesn&apos;t have an associated workflow template
              or the template data is incomplete.
            </p>
            <div className="text-sm text-muted-foreground bg-muted p-4 rounded-lg">
              <p className="font-medium mb-2">Available Application Data:</p>
              <ul className="text-left space-y-1">
                <li>• Application ID: {application.id}</li>
                <li>• Application Number: {application.application_number}</li>
                <li>• Service Type: {application.service_type}</li>
                <li>• Status: {application.status}</li>
                <li>• Current Step: {application.current_step}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Workflow Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Workflow Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <WorkflowStepper
            stages={stages}
            currentStage={currentStage}
            onStageChange={handleStageChange}
          />
        </CardContent>
      </Card>

      {/* Current Stage Management */}
      <StageManager
        stage={stages[currentStage]}
        stageIndex={currentStage}
        formData={formData}
        uploadedDocuments={uploadedDocuments}
        onFormDataChange={handleFormDataChange}
        onDocumentUpload={handleDocumentUpload}
        onDocumentStatusUpdate={handleDocumentStatusUpdate}
        applicationId={application.id}
        currentStep={currentStage}
      />

      {/* Navigation */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={() => handleStageChange(Math.max(0, currentStage - 1))}
              disabled={currentStage === 0}
            >
              Previous Stage
            </Button>

            <div className="flex gap-3">
              <ProgressStageSelect
                applicationId={application.id}
                currentStep={
                  typeof application.current_step === "string"
                    ? parseInt(application.current_step)
                    : application.current_step
                }
                numberOfSteps={numberOfSteps}
                steps={application.steps}
              />

              <Button
                variant="outline"
                onClick={() =>
                  handleStageChange(
                    Math.min(stages.length - 1, currentStage + 1)
                  )
                }
                disabled={currentStage === stages.length - 1}
              >
                Next Stage
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
