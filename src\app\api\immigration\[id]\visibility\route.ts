import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * PATCH /api/immigration/[id]/visibility
 *
 * Updates the website visibility status of an immigration service.
 * This endpoint is specifically designed for real-time toggle functionality
 * in the admin interface.
 *
 * @param {NextRequest} request - The incoming request containing website_visible boolean
 * @param {{ params: { id: string } }} params - Route parameters containing the immigration service ID
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Immigration service ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { website_visible: websiteVisible } = body;

    if (typeof websiteVisible !== "boolean") {
      return NextResponse.json(
        { success: false, message: "website_visible must be a boolean value" },
        { status: 400 }
      );
    }

    // Update immigration service visibility in backend
    const backendUrl = `${apiUrl}/immigration/${id}/visibility`;

    const response = await fetch(backendUrl, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ website_visible: websiteVisible }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      return NextResponse.json(
        {
          success: false,
          message:
            data.message || "Failed to update immigration service visibility",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating immigration service visibility:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
