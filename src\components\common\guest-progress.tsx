"use client";
import { useGuestProgress } from "@/hooks/use-query";
import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "../ui/badge";

interface IGuestProgressProp {
  id: string;
  status: string;
  type: string;
  validate: string;
}

const GuestProgress: React.FC<IGuestProgressProp> = ({
  id,
  status,
  type,
  validate,
}) => {
  const { mutate, isPending } = useGuestProgress(validate);
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="none"
          className="select-none"
          disabled={isPending}
          aria-label="Open edit menu"
        >
          <Badge
            //  @ts-ignore

            variant={progress.find((item) => item.name === status)?.variant}
          >
            {status}
          </Badge>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {progress.map((el) => (
          <DropdownMenuItem
            key={el.name}
            className="w-full text-center cursor-pointer"
            onClick={() =>
              mutate({
                status: el.name,
                type,
                id,
              })
            }
          >
            <Badge
              //  @ts-ignore
              variant={el.variant}
              className="w-full text-center flex justify-center"
            >
              {el.name}
            </Badge>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default GuestProgress;

const progress = [
  {
    name: "Pending",
    variant: "pending",
  },
  {
    name: "Completed",
    variant: "success",
  },
];
