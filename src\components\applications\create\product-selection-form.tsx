"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Package, ArrowLeft } from "lucide-react";

const productSelectionSchema = z.object({
  immigrationProductId: z.string().min(1, "Please select a product"),
});

type FormData = z.infer<typeof productSelectionSchema>;

interface ProductSelectionFormProps {
  onNext: (data: {
    immigrationProductId: string;
    originalPrice: number;
  }) => void;
  onBack: () => void;
  initialData?: {
    immigrationProductId?: string;
  };
}

interface IImmigrationProduct {
  id: string;
  name: string;
  description?: string;
  amount: number;
  category?: string;
}

export const ProductSelectionForm: React.FC<ProductSelectionFormProps> = ({
  onNext,
  onBack,
  initialData,
}) => {
  const [products, setProducts] = useState<IImmigrationProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingProducts, setLoadingProducts] = useState(true);
  const [selectedProductPrice, setSelectedProductPrice] = useState<number>(0);

  const form = useForm<FormData>({
    resolver: zodResolver(productSelectionSchema),
    defaultValues: {
      immigrationProductId: initialData?.immigrationProductId || "",
    },
  });

  const selectedProductId = form.watch("immigrationProductId");

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoadingProducts(true);
        const response = await fetch("/api/immigration");
        if (response.ok) {
          const data = await response.json();
          setProducts(data.data || data || []);
        } else {
          throw new Error("Failed to fetch immigration products");
        }
      } catch (error) {
        console.error("Error fetching immigration products:", error);
      } finally {
        setLoadingProducts(false);
      }
    };

    fetchProducts();
  }, []);

  useEffect(() => {
    if (selectedProductId) {
      const selectedProduct = products.find((p) => p.id === selectedProductId);
      if (selectedProduct) {
        setSelectedProductPrice(selectedProduct.amount);
      }
    } else {
      setSelectedProductPrice(0);
    }
  }, [selectedProductId, products]);

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      onNext({
        immigrationProductId: data.immigrationProductId,
        originalPrice: selectedProductPrice,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Immigration Product Selection
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="immigrationProductId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Select Immigration Product</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={loadingProducts}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            loadingProducts
                              ? "Loading products..."
                              : "Select a product"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name} - ${product.amount}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedProductPrice > 0 && (
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm font-medium">
                  Selected Product Price: ${selectedProductPrice}
                </p>
              </div>
            )}

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex-1"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                type="submit"
                disabled={loading || selectedProductPrice === 0}
                className="flex-1"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Continue to Discount
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
