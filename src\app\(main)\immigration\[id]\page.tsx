import { getImmigrationById } from "@/hooks/use-server";
import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import ImmigrationEditForm from "@/components/immigration/immigration-edit-form";

const EditImmigrationPage = async ({ params }: { params: { id: string } }) => {
  const data = await getImmigrationById(params.id);

  return (
    <ContentLayout title="Edit Immigration Service">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link href="/immigration">Immigration Services</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{data?.name || "Edit Service"}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <Content>
        {data ? (
          <ImmigrationEditForm immigration={data} />
        ) : (
          <div className="text-center py-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Immigration Service Not Found
            </h2>
            <p className="text-gray-600 mb-4">
              The immigration service you&apos;re looking for doesn&apos;t exist
              or has been removed.
            </p>
            <Link
              href="/immigration"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to Immigration Services
            </Link>
          </div>
        )}
      </Content>
    </ContentLayout>
  );
};

export default EditImmigrationPage;
