"use client";

import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import revalidateTag from "@/utils/revalidate-tag";

// Toast configurations
const success = {
  style: {
    background: "#10B981",
    color: "#FFFFFF",
    border: "none",
  },
};

const failed = {
  style: {
    background: "#EF4444",
    color: "#FFFFFF",
    border: "none",
  },
};
// Notification Settings Hooks
export const useUpdateNotificationSettings = () => {
  return useMutation({
    mutationFn: async (data: INotificationSetting[]) => {
      const response = await fetch("/api/notifications/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ settings: data }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(
          error.message || "Failed to update notification settings"
        );
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Notification settings updated successfully", {
        description: "Your notification preferences have been saved",
        ...success,
      });
      revalidateTag(["notification-settings"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
          "Failed to update notification settings",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};
