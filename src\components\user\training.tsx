import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "../ui/badge";
import { Briefcase } from "lucide-react";
import { NoResults } from "@/loader/no-results";
import Progress from "../common/progress";

const Training = ({ data }: { data: Training[] }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <Briefcase className="h-5 w-5" />
          Training Program
        </CardTitle>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <NoResults
            title=""
            description="There are No purchase by this user for any training programs"
          />
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Payment Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((el) => (
                <TableRow key={el.id}>
                  <TableCell>{el.training.name}</TableCell>
                  <TableCell>€{el.amount}</TableCell>
                  <TableCell>
                    <Badge variant="success" className="capitalize">
                      {el.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Progress id={el.id} type="training" status={el.progress} />
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(el.createdAt).toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default Training;
