"use client";
import React from "react";
import { Dialog<PERSON>ontent, DialogHeader } from "../ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { DialogTitle } from "@radix-ui/react-dialog";
import { CircleX } from "lucide-react";
import { useCreateTraining, useUpdateTraining } from "@/hooks/use-query";
import ButtonLoader from "../ui/button-loader";
import { trainingSchema } from "@/utils/schema";
import FileUpload from "../common/file-upload";

interface ITrainingProp {
  training: ITraining | undefined;
}

const TrainingForm: React.FC<ITrainingProp> = ({ training }) => {
  const [service, setService] = React.useState("");
  const [highlight, setHighlight] = React.useState("");
  const form = useForm<z.infer<typeof trainingSchema>>({
    resolver: zodResolver(trainingSchema),
    defaultValues: {
      img: training?.img,
      order: training?.order,
      name: training?.name,
      amount: training?.amount,
      service: training ? training.service : [],
      highlights: training ? training.highlights : [],
    },
  });
  const { mutate: create, isPending: isCreating } = useCreateTraining();
  const { mutate: update, isPending: isUpdating } = useUpdateTraining(
    training?.id || ""
  );
  const onSubmit = (data: z.infer<typeof trainingSchema>) => {
    if (training) {
      update(data);
    } else {
      create(data);
    }
  };

  return (
    <DialogContent className="max-w-3xl max-h-[60vh] overflow-y-scroll scrollbar scrollbar-w-0">
      <DialogHeader>
        <DialogTitle>
          {training ? "Edit Training" : "Add New Training"}
        </DialogTitle>
      </DialogHeader>
      <Form {...form}>
        <FileUpload
          form={form}
          field="img"
          message="PNG,JPEG and WEBP are allowed"
          accept=".png,.jpeg,.webp"
          folder="training"
        />
        <form onSubmit={form.handleSubmit(onSubmit)} className=" space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Training Name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <div className="relative flex rounded-lg shadow-sm shadow-black/5">
                      <span className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-sm text-muted-foreground">
                        €
                      </span>
                      <Input
                        id="input-16"
                        className="-me-px rounded-e-none ps-6 shadow-none"
                        placeholder="0.00"
                        type="number"
                        {...field}
                      />
                      <span className="-z-10 inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-sm text-muted-foreground">
                        EUR
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="order"
              render={({ field }) => (
                <FormItem className="lg:col-span-2 ">
                  <FormLabel>Order</FormLabel>
                  <FormControl>
                    <Input placeholder="1" type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="lg:col-span-2 space-y-2">
              <FormLabel>Services</FormLabel>
              <div className="flex rounded-lg shadow-sm shadow-black/5">
                <Input
                  className="-me-px flex-1 rounded-e-none shadow-none focus-visible:z-10"
                  value={service}
                  onChange={(e) => setService(e.target.value)}
                  placeholder="Learn With Real Challenges"
                />
                <button
                  type="button"
                  className="inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-sm font-medium text-foreground outline-offset-2 transition-colors hover:bg-accent hover:text-foreground focus:z-10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:cursor-not-allowed disabled:opacity-50"
                  onClick={() => {
                    if (service.length > 0) {
                      form.setValue("service", [
                        ...form.watch("service"),
                        service,
                      ]);
                      setService("");
                    }
                  }}
                >
                  Add
                </button>
              </div>

              <div className="space-y-3">
                {form.watch("service").map((el, i) => (
                  <div
                    key={i}
                    className="bg-[#fafafa] dark:bg-[#18181b] p-2 rounded-md shadow-md flex relative"
                  >
                    <p>{el}</p>
                    <CircleX
                      className="absolute right-2 cursor-pointer text-red-400"
                      onClick={() => {
                        form.setValue("service", [
                          ...form
                            .watch("service")
                            .filter((item, index) => index !== i),
                        ]);
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
            <div className="lg:col-span-2 space-y-2">
              <FormLabel>Highlights</FormLabel>
              <div className="flex rounded-lg shadow-sm shadow-black/5">
                <Input
                  className="-me-px flex-1 rounded-e-none shadow-none focus-visible:z-10"
                  value={highlight}
                  onChange={(e) => setHighlight(e.target.value)}
                  placeholder="90 Contact Hrs of Coaching"
                />
                <button
                  type="button"
                  className="inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-sm font-medium text-foreground outline-offset-2 transition-colors hover:bg-accent hover:text-foreground focus:z-10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:cursor-not-allowed disabled:opacity-50"
                  onClick={() => {
                    if (highlight.length > 0) {
                      form.setValue("highlights", [
                        ...form.watch("highlights"),
                        highlight,
                      ]);
                      setHighlight("");
                    }
                  }}
                >
                  Add
                </button>
              </div>

              <div className="space-y-3">
                {form.watch("highlights").map((el, i) => (
                  <div
                    key={i}
                    className="bg-[#fafafa] dark:bg-[#18181b] p-2 rounded-md shadow-md flex relative"
                  >
                    <p>{el}</p>
                    <CircleX
                      className="absolute right-2 cursor-pointer text-red-400"
                      onClick={() => {
                        form.setValue("highlights", [
                          ...form
                            .watch("highlights")
                            .filter((item, index) => index !== i),
                        ]);
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
          {isCreating || isUpdating ? (
            <ButtonLoader />
          ) : (
            <Button>Submit</Button>
          )}
        </form>
      </Form>
    </DialogContent>
  );
};

export default TrainingForm;
