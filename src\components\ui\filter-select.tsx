"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface FilterOption {
  value: string;
  label: string;
}

interface FilterSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  options: FilterOption[];
  placeholder?: string;
  className?: string;
  label?: string;
  showClearButton?: boolean;
  onClear?: () => void;
}

export function FilterSelect({
  value,
  onValueChange,
  options,
  placeholder = "Select...",
  className = "w-[150px]",
  label,
  showClearButton = false,
  onClear,
}: FilterSelectProps) {
  return (
    <div className="flex flex-col gap-2">
      {label && <label className="text-sm font-medium">{label}</label>}
      <div className="flex gap-2 items-center">
        <Select value={value} onValueChange={onValueChange}>
          <SelectTrigger className={className}>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {showClearButton && value !== "all" && onClear && (
          <button
            onClick={onClear}
            className="text-sm text-muted-foreground hover:text-foreground underline"
            type="button"
          >
            Clear
          </button>
        )}
      </div>
    </div>
  );
}
