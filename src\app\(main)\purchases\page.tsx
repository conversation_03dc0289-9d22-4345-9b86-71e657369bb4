"use client";
import React, { useState, useEffect, useCallback } from "react";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import Link from "next/link";
import { useRouter } from "next/navigation";
import PurchasesDataTable from "@/components/table/purchases/purchases-datatable";
import { createPurchasesColumns } from "@/components/table/purchases/purchases-columns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useSession } from "next-auth/react";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { format } from "date-fns";

import { toast } from "sonner";
import { useAuthError<PERSON><PERSON><PERSON> } from "@/hooks/use-auth-error-handler";

const PurchasesPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { handle401Error } = useAuthErrorHandler();
  const [payments, setPayments] = useState<PaymentData[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalItems: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState<{
    serviceType?: string;
    paymentType?: string;
    status?: string;
    progress?: string;
    startDate?: string;
    endDate?: string;
  }>({});
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const [isLoading, setIsLoading] = useState(true);

  // Fetch payment history - memoized to prevent infinite re-renders
  const fetchPayments = useCallback(
    async (page: number = 1, currentFilters: typeof filters = {}) => {
      if (!session) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        // Build query parameters
        const params = new URLSearchParams({
          page: page.toString(),
          limit: "10", // Fixed limit to avoid dependency on pagination state
        });

        Object.entries(currentFilters).forEach(([key, value]) => {
          if (value) {
            params.append(key, value);
          }
        });

        // Use the Next.js API route instead of direct backend call
        const response = await fetch(
          `/api/v2/payment/history?${params.toString()}`,
          {
            headers: {
              "Content-Type": "application/json",
            },
            cache: "no-store",
          }
        );

        if (response.ok) {
          const result: PaymentHistoryResponse = await response.json();

          setPayments(result.data || []);
          setPagination({
            page: result.page || 1,
            limit: result.limit || 10,
            totalItems: result.totalItems || 0,
            totalPages: result.totalPages || 0,
          });
        } else {
          const errorData = await response.json().catch(() => ({}));
          console.error(
            "Failed to fetch payments, status:",
            response.status,
            errorData
          );
          setPayments([]);
          setPagination({
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
          });
        }
      } catch (error) {
        console.error("Error fetching payments:", error);
        setPayments([]);
        setPagination({
          page: 1,
          limit: 10,
          totalItems: 0,
          totalPages: 0,
        });
      } finally {
        setIsLoading(false);
      }
    },
    [session]
  ); // Include session in dependency array

  // Initial load - handle authentication and fetch data
  useEffect(() => {
    if (status === "loading") {
      return; // Still loading session
    }

    if (status === "unauthenticated") {
      // Redirect to signin with return URL
      const returnUrl = encodeURIComponent("/purchases");
      router.push(`/signin?callbackUrl=${returnUrl}`);
      return;
    }

    if (status === "authenticated" && session) {
      fetchPayments();
    }
  }, [fetchPayments, status, session, router]);

  // Handle page changes - memoized to prevent re-renders
  const handlePageChange = useCallback(
    (newPage: number) => {
      setPagination((prev) => ({ ...prev, page: newPage }));
      fetchPayments(newPage, filters);
    },
    [fetchPayments, filters]
  );

  // Handle filter changes - memoized to prevent re-renders
  const handleFiltersChange = useCallback(
    (newFilters: typeof filters) => {
      setFilters(newFilters);
      setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
      fetchPayments(1, newFilters);
    },
    [fetchPayments]
  );

  // Handle date range changes
  const handleDateRangeChange = useCallback(
    (range: DateRange | undefined) => {
      // Validate date range
      if (range?.from && range?.to && range.from > range.to) {
        // If start date is after end date, don't update
        return;
      }

      setDateRange(range);

      const dateFilters = {
        ...filters,
        startDate: range?.from ? format(range.from, "yyyy-MM-dd") : undefined,
        endDate: range?.to ? format(range.to, "yyyy-MM-dd") : undefined,
      };

      setFilters(dateFilters);
      setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
      fetchPayments(1, dateFilters);
    },
    [filters, fetchPayments]
  );

  // Handle progress update for individual payments
  const handleProgressUpdate = useCallback(
    async (paymentId: string, newProgress: string) => {
      try {
        const response = await fetch("/api/v2/payment/progress", {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentId,
            progress: newProgress,
          }),
        });

        const result = await response.json();

        if (response.ok) {
          toast.success("Progress updated successfully");
          // Refresh the data to show updated progress
          fetchPayments(pagination.page, filters);
        } else {
          if (response.status === 401) {
            handle401Error();
            return;
          }
          toast.error(result.message || "Failed to update progress");
        }
      } catch (error) {
        console.error("Error updating progress:", error);
        toast.error("Failed to update progress");
      }
    },
    [pagination.page, filters, fetchPayments, handle401Error]
  );

  // Create columns with progress update handler
  const columns = React.useMemo(
    () => createPurchasesColumns(handleProgressUpdate),
    [handleProgressUpdate]
  );

  // Show loading state while session is loading
  if (status === "loading") {
    return (
      <ContentLayout title="Purchases">
        <div className="flex justify-between">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Purchases</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <Content>
          <Card>
            <CardContent className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p>Loading session...</p>
              </div>
            </CardContent>
          </Card>
        </Content>
      </ContentLayout>
    );
  }

  // Redirect handled in useEffect, no need for unauthenticated state render

  return (
    <ContentLayout title="Purchases">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Purchases</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <Content>
        {/* Main Data Table */}
        <Card>
          <CardHeader>
            <CardTitle>Payment History</CardTitle>
            <CardDescription>
              Manage and track all payment transactions across all services
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filters Section */}
            <div className="mb-6 space-y-4">
              {/* Date Range Filter */}
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                <div className="flex flex-col gap-2">
                  <label className="text-sm font-medium">
                    Date Range Filter
                  </label>
                  <div className="flex gap-2 items-center">
                    <DateRangePicker
                      value={dateRange}
                      onChange={handleDateRangeChange}
                      placeholder="Select date range"
                      className="w-full sm:w-[300px]"
                    />
                    {dateRange?.from && (
                      <button
                        onClick={() => handleDateRangeChange(undefined)}
                        className="text-sm text-muted-foreground hover:text-foreground underline"
                        type="button"
                      >
                        Clear
                      </button>
                    )}
                  </div>
                </div>
                {dateRange?.from && (
                  <div className="text-sm text-muted-foreground mt-6">
                    {dateRange.to
                      ? `Showing payments from ${format(dateRange.from, "MMM dd, yyyy")} to ${format(dateRange.to, "MMM dd, yyyy")}`
                      : `Showing payments from ${format(dateRange.from, "MMM dd, yyyy")}`}
                  </div>
                )}
              </div>

              {/* Additional Filters Info */}
              <div className="mt-4 text-sm text-muted-foreground">
                Use the filters below the search bar to filter by Service Type,
                Payment Type, Payment Status, and Progress Status.
              </div>
            </div>

            <PurchasesDataTable
              columns={columns}
              data={payments}
              pagination={pagination}
              onPageChange={handlePageChange}
              onFiltersChange={handleFiltersChange}
              isLoading={isLoading}
            />
          </CardContent>
        </Card>
      </Content>
    </ContentLayout>
  );
};

export default PurchasesPage;
