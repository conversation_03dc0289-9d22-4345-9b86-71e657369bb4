"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useUpdateDocumentStatus } from "@/hooks/use-query";
import { Settings, Loader2 } from "lucide-react";

interface DocumentStatusManagerProps {
  document: IApplicationDocument;
  applicationId: string;
  onStatusUpdate?: (newStatus: DocumentStatus, reason?: string) => void;
}

// Document Status enum values
const DocumentStatusValues = {
  PENDING: "Pending",
  UNDER_REVIEW: "Under_Review",
  APPROVED: "Approved",
  REJECTED: "Rejected",
  REQUIRED_REVISION: "Required_Revision",
} as const;

type DocumentStatusType =
  (typeof DocumentStatusValues)[keyof typeof DocumentStatusValues];

// Statuses that require a reason
const statusesRequiringReason: DocumentStatusType[] = [
  DocumentStatusValues.REJECTED,
  DocumentStatusValues.REQUIRED_REVISION,
  DocumentStatusValues.UNDER_REVIEW,
];

// Form schema with conditional validation
const createFormSchema = (status: DocumentStatusType) => {
  const baseSchema = {
    status: z.enum([
      DocumentStatusValues.PENDING,
      DocumentStatusValues.UNDER_REVIEW,
      DocumentStatusValues.APPROVED,
      DocumentStatusValues.REJECTED,
      DocumentStatusValues.REQUIRED_REVISION,
    ] as const),
  };

  if (statusesRequiringReason.includes(status)) {
    return z.object({
      ...baseSchema,
      reason: z.string().min(1, "Reason is required for this status"),
    });
  }

  return z.object({
    ...baseSchema,
    reason: z.string().optional(),
  });
};

type FormData = {
  status: DocumentStatusType;
  reason?: string;
};

const getStatusColor = (status?: DocumentStatusType) => {
  switch (status) {
    case DocumentStatusValues.APPROVED:
      return "bg-green-100 text-green-800 border-green-200";
    case DocumentStatusValues.REJECTED:
      return "bg-red-100 text-red-800 border-red-200";
    case DocumentStatusValues.UNDER_REVIEW:
      return "bg-blue-100 text-blue-800 border-blue-200";
    case DocumentStatusValues.REQUIRED_REVISION:
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case DocumentStatusValues.PENDING:
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getStatusLabel = (status?: DocumentStatusType) => {
  switch (status) {
    case DocumentStatusValues.UNDER_REVIEW:
      return "Under Review";
    case DocumentStatusValues.REQUIRED_REVISION:
      return "Required Revision";
    default:
      return status || DocumentStatusValues.PENDING;
  }
};

export const DocumentStatusManager: React.FC<DocumentStatusManagerProps> = ({
  document,
  applicationId,
  onStatusUpdate,
}) => {
  const [open, setOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<DocumentStatusType>(
    (document.status as DocumentStatusType) || DocumentStatusValues.PENDING
  );

  const updateStatusMutation = useUpdateDocumentStatus(applicationId);

  const form = useForm<FormData>({
    resolver: zodResolver(createFormSchema(selectedStatus)),
    defaultValues: {
      status:
        (document.status as DocumentStatusType) || DocumentStatusValues.PENDING,
      reason: document.reason || "",
    },
  });

  const watchedStatus = form.watch("status");

  // Update form schema when status changes
  React.useEffect(() => {
    setSelectedStatus(watchedStatus);
    // Clear reason if not required for the new status
    if (!statusesRequiringReason.includes(watchedStatus)) {
      form.setValue("reason", "");
    }
  }, [watchedStatus, form]);

  const onSubmit = async (data: FormData) => {
    try {
      await updateStatusMutation.mutateAsync({
        documentId: document.id,
        status: data.status as DocumentStatus,
        reason: data.reason,
      });

      setOpen(false);
      // Call the callback with the new status and reason
      onStatusUpdate?.(data.status as DocumentStatus, data.reason);
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  const currentStatus =
    (document.status as DocumentStatusType) || DocumentStatusValues.PENDING;
  const showReasonField = statusesRequiringReason.includes(selectedStatus);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 gap-2">
          <Settings className="h-3 w-3" />
          <Badge variant="outline" className={getStatusColor(currentStatus)}>
            {getStatusLabel(currentStatus)}
          </Badge>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Document Status</DialogTitle>
          <DialogDescription>
            Update the status for &quot;{document.document_name}&quot;
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={DocumentStatusValues.PENDING}>
                        {getStatusLabel(DocumentStatusValues.PENDING)}
                      </SelectItem>
                      <SelectItem value={DocumentStatusValues.UNDER_REVIEW}>
                        {getStatusLabel(DocumentStatusValues.UNDER_REVIEW)}
                      </SelectItem>
                      <SelectItem value={DocumentStatusValues.APPROVED}>
                        {getStatusLabel(DocumentStatusValues.APPROVED)}
                      </SelectItem>
                      <SelectItem value={DocumentStatusValues.REJECTED}>
                        {getStatusLabel(DocumentStatusValues.REJECTED)}
                      </SelectItem>
                      <SelectItem
                        value={DocumentStatusValues.REQUIRED_REVISION}
                      >
                        {getStatusLabel(DocumentStatusValues.REQUIRED_REVISION)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {showReasonField && (
              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Reason <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Please provide a reason for this status change..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={updateStatusMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateStatusMutation.isPending}>
                {updateStatusMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Update Status
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
