import type { Metadata } from "next";
import "../globals.css";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import React from "react";
import { GeistSans } from "geist/font/sans";
import NextAuthProvider from "@/provider/next-auth";
import TanStackProvider from "@/provider/tanstack";
export const metadata: Metadata = {
  title: "Admin Panel",
  description: "Careerireland admin panel",
};

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={GeistSans.className} suppressHydrationWarning>
        <NextAuthProvider>
          <TanStackProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="light"
              enableSystem
              disableTransitionOnChange
            >
              {children}
              <Toaster />
            </ThemeProvider>
          </TanStackProvider>
        </NextAuthProvider>
      </body>
    </html>
  );
}
