"use client";

import React, { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { useToggleImmigrationVisibility } from "@/hooks/use-query";
import { Loader2 } from "lucide-react";

/**
 * Props for the VisibilityToggle component
 * @param immigrationId - The ID of the immigration service to toggle
 * @param initialVisibility - The current visibility state
 * @param disabled - Whether the toggle should be disabled
 */
interface VisibilityToggleProps {
  immigrationId: string;
  initialVisibility: boolean;
  disabled?: boolean;
}

/**
 * VisibilityToggle Component
 *
 * A reusable toggle component for controlling immigration service website visibility.
 * Features optimistic updates with automatic rollback on API errors.
 * Provides immediate visual feedback and loading states.
 *
 * @param props - The component props
 * @returns JSX element containing the visibility toggle
 */

export const VisibilityToggle: React.FC<VisibilityToggleProps> = ({
  immigrationId,
  initialVisibility,
  disabled = false,
}) => {
  const [isVisible, setIsVisible] = useState(initialVisibility);
  const { mutate: toggleVisibility, isPending } =
    useToggleImmigrationVisibility();

  /**
   * Handles toggle state changes with optimistic updates
   * Updates UI immediately and reverts on API error
   *
   * @param {boolean} newValue - The new visibility state
   */
  const handleToggle = (newValue: boolean) => {
    // Optimistically update the UI for immediate feedback
    setIsVisible(newValue);

    // Call API to update backend
    toggleVisibility(
      { id: immigrationId, websiteVisible: newValue },
      {
        onError: () => {
          // Revert the optimistic update on error
          setIsVisible(!newValue);
        },
      }
    );
  };

  return (
    <div className="flex items-center gap-2">
      {isPending && (
        <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
      )}
      <Switch
        checked={isVisible}
        onCheckedChange={handleToggle}
        disabled={disabled || isPending}
        aria-label={`Toggle website visibility for immigration service`}
      />
      <span className="text-sm text-muted-foreground">
        {isVisible ? "Visible" : "Hidden"}
      </span>
    </div>
  );
};
