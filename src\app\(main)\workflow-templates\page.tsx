"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Plus } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { WorkflowTemplatesDataTable } from "@/components/workflow-templates/workflow-templates-datatable";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";
import { apiUrl } from "@/utils/urls";

interface WorkflowTemplatesPageProps {}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const WorkflowTemplatesPage: React.FC<WorkflowTemplatesPageProps> = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { handle401Error } = useAuthErrorHand<PERSON>();

  const [templates, setTemplates] = useState<IWorkflowTemplate[]>([]);
  const [immigrationPackages, setImmigrationPackages] = useState<IImmigration[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  // Filters
  const [searchQuery, setSearchQuery] = useState("");
  const [serviceTypeFilter, setServiceTypeFilter] = useState("");
  const [activeFilter, setActiveFilter] = useState<boolean | undefined>(
    undefined
  );

  const fetchImmigrationPackages = useCallback(async () => {
    if (!session?.backendTokens?.accessToken) return;

    try {
      const response = await fetch(`${apiUrl}/immigration`, {
        headers: {
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setImmigrationPackages(data || []);
      }
    } catch (error) {
      console.error("Error fetching immigration packages:", error);
    }
  }, [session]);

  const fetchTemplates = useCallback(
    async (
      page: number = 1,
      search?: string,
      serviceType?: string,
      isActive?: boolean
    ) => {
      if (!session?.backendTokens?.accessToken) return;

      try {
        setLoading(true);
        const queryParams = new URLSearchParams({
          page: page.toString(),
          limit: pagination.limit.toString(),
          ...(search && { search }),
          ...(serviceType && { serviceType }),
          ...(isActive !== undefined && { isActive: isActive.toString() }),
        });

        const response = await fetch(
          `${apiUrl}/workflow-templates?${queryParams}`,
          {
            headers: {
              Authorization: `Bearer ${session.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          if (response.status === 401) {
            handle401Error();
            return;
          }
          throw new Error("Failed to fetch workflow templates");
        }

        const data = await response.json();
        setTemplates(data.data || []);
        setPagination(data.pagination || pagination);
      } catch (error) {
        console.error("Error fetching workflow templates:", error);
        toast.error("Failed to fetch workflow templates");
        setTemplates([]);
      } finally {
        setLoading(false);
      }
    },
    [session, pagination.limit, handle401Error]
  );

  useEffect(() => {
    if (session?.backendTokens?.accessToken) {
      fetchTemplates(1, searchQuery, serviceTypeFilter, activeFilter);
      fetchImmigrationPackages();
    }
  }, [session, searchQuery, serviceTypeFilter, activeFilter, fetchImmigrationPackages]);

  const handlePageChange = useCallback(
    (newPage: number) => {
      setPagination((prev) => ({ ...prev, page: newPage }));
      fetchTemplates(newPage, searchQuery, serviceTypeFilter, activeFilter);
    },
    [fetchTemplates, searchQuery, serviceTypeFilter, activeFilter]
  );

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const handleServiceTypeFilter = useCallback((serviceType: string) => {
    setServiceTypeFilter(serviceType);
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const handleActiveFilter = useCallback((isActive: boolean | undefined) => {
    setActiveFilter(isActive);
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const handleUpdateSuccess = useCallback(() => {
    fetchTemplates(
      pagination.page,
      searchQuery,
      serviceTypeFilter,
      activeFilter
    );
    toast.success("Workflow template updated successfully");
  }, [
    fetchTemplates,
    pagination.page,
    searchQuery,
    serviceTypeFilter,
    activeFilter,
  ]);

  const handleDeleteSuccess = useCallback(() => {
    fetchTemplates(
      pagination.page,
      searchQuery,
      serviceTypeFilter,
      activeFilter
    );
    toast.success("Workflow template deleted successfully");
  }, [
    fetchTemplates,
    pagination.page,
    searchQuery,
    serviceTypeFilter,
    activeFilter,
  ]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Workflow Templates
          </h1>
          <p className="text-muted-foreground">
            Manage workflow templates for immigration services
          </p>
        </div>
        <Button onClick={() => router.push("/workflow-templates/create")}>
          <Plus className="mr-2 h-4 w-4" />
          Add Workflow Template
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Workflow Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <WorkflowTemplatesDataTable
            data={templates}
            loading={loading}
            pagination={pagination}
            onPageChange={handlePageChange}
            onSearch={handleSearch}
            onServiceTypeFilter={handleServiceTypeFilter}
            onActiveFilter={handleActiveFilter}
            onUpdateSuccess={handleUpdateSuccess}
            onDeleteSuccess={handleDeleteSuccess}
            immigrationPackages={immigrationPackages}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkflowTemplatesPage;
