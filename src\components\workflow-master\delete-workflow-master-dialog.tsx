"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useDeleteWorkflowMaster } from "@/hooks/use-query";

interface DeleteWorkflowMasterDialogProps {
  workflow: IWorkflowMaster | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export const DeleteWorkflowMasterDialog: React.FC<
  DeleteWorkflowMasterDialogProps
> = ({ workflow, open, onOpenChange, onSuccess }) => {
  const deleteMutation = useDeleteWorkflowMaster();

  const handleDelete = async () => {
    if (!workflow?.id) return;

    try {
      await deleteMutation.mutateAsync(workflow.id);
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  if (!workflow) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Workflow Master</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the workflow master &quot;
            {workflow.name}&quot;? This action cannot be undone and will
            permanently remove the workflow master from the system.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteMutation.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleteMutation.isPending ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
