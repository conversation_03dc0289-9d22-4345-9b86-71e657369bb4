import React from "react";
import Link from "next/link";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { getApplicationById } from "@/hooks/use-server";
import { ApplicationDetail } from "@/components/applications/application-detail";
import { EstimatedCompletionField } from "@/components/applications/estimated-completion-field";
import { NoteField } from "@/components/applications/note-field";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import {
  ArrowLeft,
  Calendar,
  Clock,
  User,
  Mail,
  Phone,
  FileText,
  Briefcase,
} from "lucide-react";
import { Button } from "@/components/ui/button";

const ApplicationDetailPage = async ({
  params,
}: {
  params: { id: string };
}) => {
  const application = await getApplicationById(params.id);

  if (!application) {
    return (
      <ContentLayout title="Application Not Found">
        <div className="flex justify-between">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/applications">Applications</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Not Found</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <Content>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <h2 className="text-2xl font-bold text-muted-foreground mb-2">
                  Application Not Found
                </h2>
                <p className="text-muted-foreground mb-4">
                  The application you&apos;re looking for doesn&apos;t exist or
                  you don&apos;t have permission to view it.
                </p>
                <Button asChild>
                  <Link href="/applications">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Applications
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </Content>
      </ContentLayout>
    );
  }

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high":
        return "destructive";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      case "critical":
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <ContentLayout title={`Application ${application.application_number}`}>
      <div className="flex justify-between items-center">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/applications">Applications</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{application.application_number}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button variant="outline" asChild>
          <Link href="/applications">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Applications
          </Link>
        </Button>
      </div>

      <Content>
        {/* Application Overview */}
        <Card className="overflow-hidden border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-1.5 bg-white/20 rounded-lg backdrop-blur-sm">
                  <FileText className="h-5 w-5" />
                </div>
                <div>
                  <CardTitle className="text-xl font-bold">
                    Application {application.application_number}
                  </CardTitle>
                  <p className="text-blue-100 text-xs">
                    Submitted{" "}
                    {format(new Date(application.created_at), "MMM dd, yyyy")}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={getPriorityBadgeVariant(application.priority_level)}
                  className="capitalize bg-white/20 text-white border-white/30 hover:bg-white/30 transition-colors"
                >
                  {application.priority_level} Priority
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="group p-3 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-950/20 dark:to-blue-900/20 border border-blue-200/50 dark:border-blue-800/50 hover:shadow-md transition-all duration-200">
                <div className="flex items-center gap-2 mb-1">
                  <div className="p-1.5 bg-blue-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
                    <Briefcase className="h-3 w-3" />
                  </div>
                  <p className="text-xs font-medium text-blue-700 dark:text-blue-300">
                    Service
                  </p>
                </div>
                <div className="space-y-0.5">
                  <p className="text-sm font-semibold capitalize text-blue-900 dark:text-blue-100">
                    {application.service_name || application.service_type}
                  </p>
                  {application.service_name && (
                    <p className="text-xs text-blue-700 dark:text-blue-300 capitalize">
                      Type: {application.service_type}
                    </p>
                  )}
                </div>
              </div>

              <div className="group p-3 rounded-lg bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-950/20 dark:to-green-900/20 border border-green-200/50 dark:border-green-800/50 hover:shadow-md transition-all duration-200">
                <div className="flex items-center gap-2 mb-1">
                  <div className="p-1.5 bg-green-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
                    <Clock className="h-3 w-3" />
                  </div>
                  <p className="text-xs font-medium text-green-700 dark:text-green-300">
                    Current Step
                  </p>
                </div>
                <p className="text-sm font-semibold text-green-900 dark:text-green-100">
                  Step {application.current_step}
                </p>
              </div>

              <div className="group p-3 rounded-lg bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-950/20 dark:to-purple-900/20 border border-purple-200/50 dark:border-purple-800/50 hover:shadow-md transition-all duration-200">
                <div className="flex items-center gap-2 mb-1">
                  <div className="p-1.5 bg-purple-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
                    <Calendar className="h-3 w-3" />
                  </div>
                  <p className="text-xs font-medium text-purple-700 dark:text-purple-300">
                    Created
                  </p>
                </div>
                <p className="text-sm font-semibold text-purple-900 dark:text-purple-100">
                  {format(new Date(application.created_at), "MMM dd, yyyy")}
                </p>
              </div>

              <div className="group p-3 rounded-lg bg-gradient-to-br from-orange-50 to-orange-100/50 dark:from-orange-950/20 dark:to-orange-900/20 border border-orange-200/50 dark:border-orange-800/50 hover:shadow-md transition-all duration-200">
                <div className="flex items-center gap-2 mb-1">
                  <div className="p-1.5 bg-orange-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
                    <Clock className="h-3 w-3" />
                  </div>
                  <p className="text-xs font-medium text-orange-700 dark:text-orange-300">
                    Last Updated
                  </p>
                </div>
                <p className="text-sm font-semibold text-orange-900 dark:text-orange-100">
                  {format(new Date(application.updated_at), "MMM dd, yyyy")}
                </p>
              </div>
            </div>

            {/* User/Guest Information */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 mb-4">
                <div className="p-1.5 bg-indigo-500 text-white rounded-lg">
                  <User className="h-4 w-4" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {(application as any).user
                    ? "User Information"
                    : "Guest Information"}
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="group p-3 rounded-lg bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-700/50 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="p-1.5 bg-gray-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
                      <User className="h-3 w-3" />
                    </div>
                    <p className="text-xs font-medium text-gray-600 dark:text-gray-400">
                      Name
                    </p>
                  </div>
                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                    {(application as any).user?.name ||
                      application.guest?.name ||
                      "N/A"}
                  </p>
                </div>

                <div className="group p-3 rounded-lg bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-700/50 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="p-1.5 bg-gray-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
                      <Mail className="h-3 w-3" />
                    </div>
                    <p className="text-xs font-medium text-gray-600 dark:text-gray-400">
                      Email
                    </p>
                  </div>
                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 break-all">
                    {(application as any).user?.email ||
                      application.guest?.email ||
                      "N/A"}
                  </p>
                </div>

                <div className="group p-3 rounded-lg bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-700/50 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="p-1.5 bg-gray-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
                      <Phone className="h-3 w-3" />
                    </div>
                    <p className="text-xs font-medium text-gray-600 dark:text-gray-400">
                      Mobile
                    </p>
                  </div>
                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                    {application.guest?.mobile || "N/A"}
                  </p>
                </div>

                <EstimatedCompletionField
                  applicationId={application.id}
                  currentDate={application.estimated_completion}
                />

                <NoteField
                  applicationId={application.id}
                  currentNote={(application as any).note}
                  className="md:col-span-2 lg:col-span-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Application Detail Component */}
        <ApplicationDetail application={application} />
      </Content>
    </ContentLayout>
  );
};

export default ApplicationDetailPage;
